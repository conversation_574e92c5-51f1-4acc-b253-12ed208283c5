using System;
using System.Numerics;
using Dalamud.Interface.Windowing;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;

namespace PvPLinePlugin.Windows;

public class ConfigWindow : Window, IDisposable
{
    private Configuration Configuration;
    private Plugin Plugin;

    public ConfigWindow(Plugin plugin) : base("PvP Line Plugin Configuration###PvPLinePluginConfig")
    {
        Flags = ImGuiWindowFlags.NoCollapse;

        Size = new Vector2(650, 700);
        SizeCondition = ImGuiCond.FirstUseEver;

        Configuration = plugin.Configuration;
        Plugin = plugin;
    }

    public void Dispose() { }

    public override void Draw()
    {
        // Main enable/disable at the top
        var enabled = Configuration.Enabled;
        if (ImGui.Checkbox("Enable PvP Lines", ref enabled))
        {
            Configuration.Enabled = enabled;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Master on/off switch for the entire plugin.\nDisable when you don't want any lines displayed.");
        }

        ImGui.Separator();
        ImGui.Spacing();

        // Tab bar for organized settings
        if (ImGui.BeginTabBar("PvPLinePluginTabs"))
        {
            DrawAppearanceTab();
            DrawDistanceTab();
            DrawPvPSettingsTab();
            DrawEnemyAllyTab();
            DrawJobRoleTab();
            DrawLowHealthTab();
            DrawInformationOverlaysTab();
            DrawPlayerListTab();

            ImGui.EndTabBar();
        }
    }

    private void DrawAppearanceTab()
    {
        if (ImGui.BeginTabItem("Appearance"))
        {
            ImGui.TextWrapped("Customize how enemy indicators look on your screen.");
            ImGui.Spacing();

            // Indicator Type Selection
            ImGui.Text("Indicator Type:");
            var currentIndicator = (int)Configuration.IndicatorType;
            var indicatorNames = new[] { "Lines", "Outlines", "Nameplates", "Icons", "Directional Arrows", "Health Bars", "Combination" };

            if (ImGui.Combo("##IndicatorType", ref currentIndicator, indicatorNames, indicatorNames.Length))
            {
                Configuration.IndicatorType = (IndicatorType)currentIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Choose how you want to see enemy indicators:\n" +
                               "• Lines: Draw lines from you to enemies (current)\n" +
                               "• Outlines: Colored outlines around enemy players\n" +
                               "• Nameplates: Enhanced nameplates above enemies\n" +
                               "• Icons: Small icons showing enemy positions\n" +
                               "• Directional Arrows: Arrows pointing to off-screen enemies\n" +
                               "• Health Bars: Floating health bars above enemies\n" +
                               "• Combination: Mix of multiple indicator types");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Enhanced Visual Settings
            DrawEnhancedVisualSettings();

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Show settings based on selected indicator type
            switch (Configuration.IndicatorType)
            {
                case IndicatorType.Lines:
                    DrawLineSettings();
                    break;
                case IndicatorType.Outlines:
                    DrawOutlineSettings();
                    break;
                case IndicatorType.Nameplates:
                    DrawNameplateSettings();
                    break;
                case IndicatorType.Icons:
                    DrawIconSettings();
                    break;
                case IndicatorType.DirectionalArrows:
                    DrawArrowSettings();
                    break;
                case IndicatorType.HealthBars:
                    DrawHealthBarSettings();
                    break;
                case IndicatorType.Combination:
                    DrawCombinationSettings();
                    break;
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawLineSettings()
    {
        ImGui.Text("Line Settings:");

        // Group related settings to reduce Save() calls
        var lineColor = Configuration.LineColor;
        var lineThickness = Configuration.LineThickness;
        var pulseIndicators = Configuration.PulseIndicators;
        var hasChanges = false;

        if (ImGui.ColorEdit4("Line Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            hasChanges = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Choose the color and transparency of lines.\nRecommended: Red for aggression, Yellow for visibility, Blue for subtlety.\nLower alpha (transparency) makes lines less intrusive.");
        }

        if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 1.0f, 10.0f))
        {
            Configuration.LineThickness = lineThickness;
            hasChanges = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Controls how thick the lines appear.\n1-2px: Subtle, minimal\n3-4px: Good balance\n5-7px: High visibility\n8-10px: Maximum visibility but may be distracting");
        }

        if (ImGui.Checkbox("Pulse Effect", ref pulseIndicators))
        {
            Configuration.PulseIndicators = pulseIndicators;
            hasChanges = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Add a pulsing animation effect to lines for better visibility.");
        }

        // Save once if any changes were made
        if (hasChanges)
        {
            Configuration.Save();
        }
    }

    private void DrawOutlineSettings()
    {
        ImGui.Text("Outline Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Outline Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        var outlineThickness = Configuration.OutlineThickness;
        if (ImGui.SliderFloat("Outline Thickness", ref outlineThickness, 1.0f, 10.0f))
        {
            Configuration.OutlineThickness = outlineThickness;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Thickness of the outline around enemy players.");
        }
    }

    private void DrawNameplateSettings()
    {
        ImGui.Text("Nameplate Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Nameplate Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Enhanced nameplates will appear above enemy players with job icons and health information.");
    }

    private void DrawIconSettings()
    {
        ImGui.Text("Icon Settings:");

        var iconSize = Configuration.IconSize;
        if (ImGui.SliderFloat("Icon Size", ref iconSize, 10.0f, 50.0f))
        {
            Configuration.IconSize = iconSize;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Icon Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Small icons will appear on screen showing enemy positions and job types.");
    }

    private void DrawArrowSettings()
    {
        ImGui.Text("Directional Arrow Settings:");

        var arrowSize = Configuration.ArrowSize;
        if (ImGui.SliderFloat("Arrow Size", ref arrowSize, 10.0f, 40.0f))
        {
            Configuration.ArrowSize = arrowSize;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Arrow Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Arrows will point toward off-screen enemies from the edges of your screen.");
    }

    private void DrawHealthBarSettings()
    {
        ImGui.Text("Health Bar Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Health Bar Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Floating health bars will appear above enemy players showing their current HP.");
    }

    private void DrawCombinationSettings()
    {
        ImGui.Text("Combination Settings:");
        ImGui.TextWrapped("Mix and match multiple indicator types. Configure each type above by temporarily switching to it.");

        var showDirectionalArrows = Configuration.ShowDirectionalArrows;
        if (ImGui.Checkbox("Include Directional Arrows", ref showDirectionalArrows))
        {
            Configuration.ShowDirectionalArrows = showDirectionalArrows;
            Configuration.Save();
        }

        ImGui.TextWrapped("Note: Combination mode will use settings from each individual indicator type.");
    }

    private void DrawDistanceTab()
    {
        if (ImGui.BeginTabItem("Distance"))
        {
            ImGui.TextWrapped("Control how far lines reach and distance information display.");
            ImGui.Spacing();

            var maxDistance = Configuration.MaxDistance;
            if (ImGui.SliderFloat("Max Distance (yalms)", ref maxDistance, 10.0f, 100.0f))
            {
                Configuration.MaxDistance = maxDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Only draws lines to enemies within this distance.\n20-30y: Close combat (melee DPS, tanks)\n40-50y: Balanced for most jobs\n60-80y: Long-range (casters, ranged DPS)\n100y: Maximum awareness (may cause clutter)");
            }

            var showDistance = Configuration.ShowDistance;
            if (ImGui.Checkbox("Show Distance Text", ref showDistance))
            {
                Configuration.ShowDistance = showDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays actual distance in yalms next to each line.\nUseful for learning optimal engagement ranges.\nDisable to reduce screen clutter.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawPvPSettingsTab()
    {
        if (ImGui.BeginTabItem("PvP Settings"))
        {
            ImGui.TextWrapped("Configure when and how the plugin activates in PvP content.");
            ImGui.Spacing();

            var onlyInPvP = Configuration.OnlyInPvP;
            if (ImGui.Checkbox("Only Show in PvP Zones", ref onlyInPvP))
            {
                Configuration.OnlyInPvP = onlyInPvP;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Restricts plugin to PvP areas only (Frontlines, Rival Wings, Crystalline Conflict, Wolves' Den).\nRecommended: Keep enabled to avoid marking friendly players as enemies in PvE content.");
            }

            var showInCombatOnly = Configuration.ShowInCombatOnly;
            if (ImGui.Checkbox("Only Show During Combat", ref showInCombatOnly))
            {
                Configuration.ShowInCombatOnly = showInCombatOnly;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Lines only appear when you're actively in combat (hotbars turn red).\nEnable: Reduces visual noise during downtime\nDisable: Constant enemy awareness and positioning");
            }

            var showPlayerNames = Configuration.ShowPlayerNames;
            if (ImGui.Checkbox("Show Enemy Player Names", ref showPlayerNames))
            {
                Configuration.ShowPlayerNames = showPlayerNames;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays character names above enemy players.\nUseful for recognizing specific players and coordinating with team.\nDisable to reduce screen clutter.");
            }

            var showStatusEffects = Configuration.ShowStatusEffects;
            if (ImGui.Checkbox("Show Status Effects", ref showStatusEffects))
            {
                Configuration.ShowStatusEffects = showStatusEffects;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays important buffs and debuffs on players.\nShows crowd control, vulnerability, damage buffs, and other tactical information.\nHelps identify opportunities and threats.");
            }

            if (Configuration.ShowStatusEffects)
            {
                ImGui.Indent();
                var showOnlyImportant = Configuration.ShowOnlyImportantStatus;
                if (ImGui.Checkbox("Show Only Negative Effects", ref showOnlyImportant))
                {
                    Configuration.ShowOnlyImportantStatus = showOnlyImportant;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("When enabled, only shows debuffs and negative status effects.\nWhen disabled, shows both buffs and debuffs.\nRecommended: Enable to reduce visual clutter.");
                }
                ImGui.Unindent();
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            var showDefensiveBuffs = Configuration.ShowDefensiveBuffs;
            if (ImGui.Checkbox("Show Defensive Buff Indicators", ref showDefensiveBuffs))
            {
                Configuration.ShowDefensiveBuffs = showDefensiveBuffs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Shows special indicators for enemies using defensive abilities.\n" +
                                "• GUARD - AVOID: Enemy has 90% damage reduction (blue line)\n" +
                                "• HEALING: Enemy is using Recuperate (cyan line)\n" +
                                "• DEFENSIVE: Enemy has other defensive buffs (light blue line)\n" +
                                "Helps you avoid wasting damage on protected enemies.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawEnemyAllyTab()
    {
        if (ImGui.BeginTabItem("Enemy/Ally"))
        {
            ImGui.TextWrapped("Configure how the plugin distinguishes between enemies and allies.");
            ImGui.Spacing();

            var showEnemies = Configuration.ShowEnemies;
            if (ImGui.Checkbox("Show Lines to Enemies", ref showEnemies))
            {
                Configuration.ShowEnemies = showEnemies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to enemy players.\nThis is the main feature for tracking opponents in PvP.");
            }

            var showAllies = Configuration.ShowAllies;
            if (ImGui.Checkbox("Show Lines to Allies", ref showAllies))
            {
                Configuration.ShowAllies = showAllies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to allied players (party/alliance members).\nUseful for keeping track of your team's position.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            var showIndicator = Configuration.ShowAllyEnemyIndicator;
            if (ImGui.Checkbox("Show 'ALLY' / 'ENEMY' Labels", ref showIndicator))
            {
                Configuration.ShowAllyEnemyIndicator = showIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display text labels above players indicating if they are allies or enemies.\nHelps quickly identify player status in chaotic battles.");
            }

            ImGui.Spacing();
            ImGui.Text("Ally Line Appearance:");

            var allyLineColor = Configuration.AllyLineColor;
            if (ImGui.ColorEdit4("Ally Line Color", ref allyLineColor))
            {
                Configuration.AllyLineColor = allyLineColor;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Color for lines to allied players.\nDefault: Green to indicate friendly status.");
            }

            var allyLineThickness = Configuration.AllyLineThickness;
            if (ImGui.SliderFloat("Ally Line Thickness", ref allyLineThickness, 1.0f, 10.0f))
            {
                Configuration.AllyLineThickness = allyLineThickness;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Thickness of lines to allied players.\nCan be different from enemy line thickness for easy distinction.");
            }

            var differentColors = Configuration.DifferentColorsForAllies;
            if (ImGui.Checkbox("Use Different Colors for Allies", ref differentColors))
            {
                Configuration.DifferentColorsForAllies = differentColors;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("When enabled, ally lines will use a blend of role colors and ally color.\nWhen disabled, all ally lines use the same ally color.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            ImGui.TextWrapped("Status Effect Information:");
            ImGui.BulletText("Vulnerable enemies get orange lines for easy targeting");
            ImGui.BulletText("Crowd controlled enemies have pulsing status text");
            ImGui.BulletText("Status effects show remaining duration when available");
            ImGui.BulletText("Priority effects (stuns, vulnerability) are shown first");

            ImGui.Spacing();
            ImGui.TextWrapped("Note: Ally detection works for party and alliance members. In some PvP modes, additional allies (like Grand Company members) may not be automatically detected.");

            ImGui.EndTabItem();
        }
    }

    private void DrawJobRoleTab()
    {
        if (ImGui.BeginTabItem("Jobs & Roles"))
        {
            ImGui.TextWrapped("Configure job and role detection for tactical PvP advantage.");
            ImGui.Spacing();

            var showPlayerJobs = Configuration.ShowPlayerJobs;
            if (ImGui.Checkbox("Show Enemy Jobs/Roles", ref showPlayerJobs))
            {
                Configuration.ShowPlayerJobs = showPlayerJobs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays job or role information above enemy players.\nHelps identify threats: Healers (priority targets), Tanks (high HP), DPS types.\nEssential for tactical PvP play.");
            }

            if (Configuration.ShowPlayerJobs)
            {
                ImGui.Indent();
                var showJobIcons = Configuration.ShowJobIcons;
                if (ImGui.Checkbox("Show Job Abbreviations (vs Role Names)", ref showJobIcons))
                {
                    Configuration.ShowJobIcons = showJobIcons;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Job Abbreviations: WHM, PLD, NIN, etc.\nRole Names: HEAL, TANK, MDPS, etc.\nJob abbreviations are more specific but take more space.");
                }
                ImGui.Unindent();
            }

            var colorCodeByRole = Configuration.ColorCodeByRole;
            if (ImGui.Checkbox("Color Lines by Role", ref colorCodeByRole))
            {
                Configuration.ColorCodeByRole = colorCodeByRole;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Colors lines based on enemy role:\nBlue = Tanks, Green = Healers, Red = Melee DPS\nOrange = Ranged Physical DPS, Purple = Magical DPS\nOverrides the custom line color setting when enabled.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Role color preview
            ImGui.TextWrapped("Role Color Preview:");
            ImGui.Spacing();

            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Tank), "■ TANK - Tanks (PLD, WAR, DRK, GNB)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Healer), "■ HEAL - Healers (WHM, SCH, AST, SGE)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MeleeDPS), "■ MDPS - Melee DPS (MNK, DRG, NIN, SAM, RPR, VPR)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.PhysicalRangedDPS), "■ PDPS - Physical Ranged (BRD, MCH, DNC)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MagicalRangedDPS), "■ CDPS - Magical Ranged (BLM, SMN, RDM, PCT)");

            ImGui.EndTabItem();
        }
    }

    private void DrawLowHealthTab()
    {
        if (ImGui.BeginTabItem("Low Health"))
        {
            ImGui.TextWrapped("Configure killable target detection and visual indicators.");
            ImGui.Spacing();

            var showLowHealthIndicator = Configuration.ShowLowHealthIndicator;
            if (ImGui.Checkbox("Enable Low Health Indicator", ref showLowHealthIndicator))
            {
                Configuration.ShowLowHealthIndicator = showLowHealthIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Highlights enemies with low health as potential kill targets.\nChanges line color, thickness, and adds health information.");
            }

            if (Configuration.ShowLowHealthIndicator)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                var lowHealthThreshold = Configuration.LowHealthThreshold;
                if (ImGui.SliderFloat("Low Health Threshold (%)", ref lowHealthThreshold, 5.0f, 50.0f))
                {
                    Configuration.LowHealthThreshold = lowHealthThreshold;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Health percentage below which enemies are marked as 'killable'.\n10-15%: Very low, almost dead\n20-25%: Good for burst combos\n30-40%: Early kill attempt threshold");
                }

                var lowHealthLineColor = Configuration.LowHealthLineColor;
                if (ImGui.ColorEdit4("Low Health Line Color", ref lowHealthLineColor))
                {
                    Configuration.LowHealthLineColor = lowHealthLineColor;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Color for lines to low health enemies.\nRecommended: Gold/Yellow for high visibility, Red for urgency.");
                }

                var lowHealthLineThickness = Configuration.LowHealthLineThickness;
                if (ImGui.SliderFloat("Low Health Line Thickness", ref lowHealthLineThickness, 2.0f, 15.0f))
                {
                    Configuration.LowHealthLineThickness = lowHealthLineThickness;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Thickness of lines to low health enemies.\nThicker lines make killable targets more obvious.");
                }

                var showHealthPercentage = Configuration.ShowHealthPercentage;
                if (ImGui.Checkbox("Show Health Percentage", ref showHealthPercentage))
                {
                    Configuration.ShowHealthPercentage = showHealthPercentage;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Shows exact health percentage (e.g., '15% HP') vs just 'KILLABLE'.\nUseful for precise timing of finishing moves.");
                }

                var pulseKillableTargets = Configuration.PulseKillableTargets;
                if (ImGui.Checkbox("Pulse Killable Targets", ref pulseKillableTargets))
                {
                    Configuration.PulseKillableTargets = pulseKillableTargets;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Makes lines and text for killable targets pulse/animate.\nDraws more attention but may be distracting.");
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                // Usage tips
                ImGui.TextWrapped("Usage Tips:");
                ImGui.BulletText("Low health targets appear with different colored, thicker lines");
                ImGui.BulletText("Health info appears above the enemy player");
                ImGui.BulletText("Use this to prioritize finishing moves and burst combos");
                ImGui.BulletText("Adjust threshold based on your job's burst potential");
                ImGui.BulletText("Healers at low health are highest priority targets");
            }

            ImGui.EndTabItem();
        }
    }



    private void DrawPlayerListTab()
    {
        if (ImGui.BeginTabItem("Player List"))
        {
            ImGui.TextWrapped("Show a window with all nearby players for easy tracking. Perfect for Crystalline Conflict matches.");
            ImGui.Spacing();

            var showPlayerList = Configuration.ShowPlayerList;
            if (ImGui.Checkbox("Show Player List Window", ref showPlayerList))
            {
                Configuration.ShowPlayerList = showPlayerList;
                Configuration.Save();

                // Toggle the window visibility
                if (showPlayerList)
                    Plugin.TogglePlayerListUI();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Show a separate window listing all nearby players with their status, health, and distance.");
            }

            if (Configuration.ShowPlayerList)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Display Options:");

                var playerListShowAllies = Configuration.PlayerListShowAllies;
                if (ImGui.Checkbox("Show Allies Tab", ref playerListShowAllies))
                {
                    Configuration.PlayerListShowAllies = playerListShowAllies;
                    Configuration.Save();
                }

                var playerListShowEnemies = Configuration.PlayerListShowEnemies;
                if (ImGui.Checkbox("Show Enemies Tab", ref playerListShowEnemies))
                {
                    Configuration.PlayerListShowEnemies = playerListShowEnemies;
                    Configuration.Save();
                }

                ImGui.Spacing();

                var playerListShowDistance = Configuration.PlayerListShowDistance;
                if (ImGui.Checkbox("Show Distance", ref playerListShowDistance))
                {
                    Configuration.PlayerListShowDistance = playerListShowDistance;
                    Configuration.Save();
                }

                var playerListShowHealth = Configuration.PlayerListShowHealth;
                if (ImGui.Checkbox("Show Health Percentage", ref playerListShowHealth))
                {
                    Configuration.PlayerListShowHealth = playerListShowHealth;
                    Configuration.Save();
                }

                var playerListShowJob = Configuration.PlayerListShowJob;
                if (ImGui.Checkbox("Show Job/Role", ref playerListShowJob))
                {
                    Configuration.PlayerListShowJob = playerListShowJob;
                    Configuration.Save();
                }

                var playerListShowStatus = Configuration.PlayerListShowStatus;
                if (ImGui.Checkbox("Show Status Effects", ref playerListShowStatus))
                {
                    Configuration.PlayerListShowStatus = playerListShowStatus;
                    Configuration.Save();
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Behavior Settings:");

                var playerListMaxDistance = Configuration.PlayerListMaxDistance;
                if (ImGui.SliderFloat("Max Distance (yalms)", ref playerListMaxDistance, 30.0f, 200.0f))
                {
                    Configuration.PlayerListMaxDistance = playerListMaxDistance;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Maximum distance to show players in the list.\n100y covers most of a Crystalline Conflict map.");
                }

                var playerListAutoHide = Configuration.PlayerListAutoHide;
                if (ImGui.Checkbox("Auto-hide outside PvP", ref playerListAutoHide))
                {
                    Configuration.PlayerListAutoHide = playerListAutoHide;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Automatically hide the player list when not in PvP zones.");
                }



                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                if (ImGui.Button("Open Player List Window"))
                {
                    Plugin.TogglePlayerListUI();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Open the player list window to see it in action.");
                }

                ImGui.SameLine();
                if (ImGui.Button("Reset Window Position"))
                {
                    // This would reset the window position - implementation depends on how you want to handle it
                    ImGui.OpenPopup("ResetInfo");
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Reset the player list window to its default position.");
                }

                if (ImGui.BeginPopup("ResetInfo"))
                {
                    ImGui.Text("Close and reopen the player list window to reset its position.");
                    ImGui.EndPopup();
                }
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawEnhancedVisualSettings()
    {
        ImGui.Text("Enhanced Visual Effects:");

        // Line Style Selection
        var currentLineStyle = (int)Configuration.LineStyle;
        var lineStyleNames = new[] { "Solid", "Dashed", "Dotted", "Gradient", "Animated", "Lightning", "Wavy", "Particles" };

        if (ImGui.Combo("Line Style", ref currentLineStyle, lineStyleNames, lineStyleNames.Length))
        {
            Configuration.LineStyle = (LineStyle)currentLineStyle;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Choose the visual style of your lines:\n" +
                           "• Solid: Classic straight lines\n" +
                           "• Dashed: Broken line segments\n" +
                           "• Dotted: Series of dots\n" +
                           "• Gradient: Fading color effect\n" +
                           "• Animated: Moving flow effect\n" +
                           "• Lightning: Jagged electric effect\n" +
                           "• Wavy: Sine wave pattern\n" +
                           "• Particles: Floating particle trail");
        }

        // Color Theme Selection
        var currentTheme = (int)Configuration.ColorTheme;
        var themeNames = new[] { "Default", "Neon", "Pastel", "High Contrast", "Colorblind Friendly", "Cyberpunk", "Nature", "Custom" };

        if (ImGui.Combo("Color Theme", ref currentTheme, themeNames, themeNames.Length))
        {
            Configuration.ColorTheme = (ColorTheme)currentTheme;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Apply different color themes to all indicators:\n" +
                           "• Default: Original colors\n" +
                           "• Neon: Bright, saturated colors\n" +
                           "• Pastel: Soft, muted colors\n" +
                           "• High Contrast: Black and white\n" +
                           "• Colorblind Friendly: Blue/yellow palette\n" +
                           "• Cyberpunk: Cyan/magenta/yellow\n" +
                           "• Nature: Earth tones\n" +
                           "• Custom: User-defined colors");
        }

        // Animation Type
        var currentAnimation = (int)Configuration.AnimationType;
        var animationNames = new[] { "None", "Pulse", "Flow", "Breathe", "Sparkle", "Glow", "Ripple" };

        if (ImGui.Combo("Animation Type", ref currentAnimation, animationNames, animationNames.Length))
        {
            Configuration.AnimationType = (AnimationType)currentAnimation;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Add animated effects to indicators:\n" +
                           "• None: No animations\n" +
                           "• Pulse: Pulsing circles at target\n" +
                           "• Flow: Moving flow along lines\n" +
                           "• Breathe: Gentle breathing effect\n" +
                           "• Sparkle: Sparkling particles\n" +
                           "• Glow: Glowing aura effect\n" +
                           "• Ripple: Ripple waves at target");
        }

        // 3D Effects
        var use3DEffects = Configuration.Use3DEffects;
        if (ImGui.Checkbox("Enable 3D Effects", ref use3DEffects))
        {
            Configuration.Use3DEffects = use3DEffects;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Add shadows and glow effects for depth");
        }

        if (Configuration.Use3DEffects)
        {
            ImGui.Indent();

            var glowIntensity = Configuration.GlowIntensity;
            if (ImGui.SliderFloat("Glow Intensity", ref glowIntensity, 0.0f, 3.0f))
            {
                Configuration.GlowIntensity = glowIntensity;
                Configuration.Save();
            }

            var shadowOffset = Configuration.ShadowOffset;
            if (ImGui.SliderFloat("Shadow Offset", ref shadowOffset, 0.0f, 10.0f))
            {
                Configuration.ShadowOffset = shadowOffset;
                Configuration.Save();
            }

            ImGui.Unindent();
        }

        // Gradient Settings
        if (Configuration.LineStyle == LineStyle.Gradient)
        {
            var useGradient = Configuration.UseGradientLines;
            if (ImGui.Checkbox("Use Gradient Lines", ref useGradient))
            {
                Configuration.UseGradientLines = useGradient;
                Configuration.Save();
            }

            if (Configuration.UseGradientLines)
            {
                ImGui.Indent();
                var gradientEnd = Configuration.GradientEndColor;
                if (ImGui.ColorEdit4("Gradient End Color", ref gradientEnd))
                {
                    Configuration.GradientEndColor = gradientEnd;
                    Configuration.Save();
                }
                ImGui.Unindent();
            }
        }

        // Distance Fading
        var fadeWithDistance = Configuration.FadeWithDistance;
        if (ImGui.Checkbox("Fade with Distance", ref fadeWithDistance))
        {
            Configuration.FadeWithDistance = fadeWithDistance;
            Configuration.Save();
        }

        if (Configuration.FadeWithDistance)
        {
            ImGui.Indent();

            var minOpacity = Configuration.MinOpacity;
            if (ImGui.SliderFloat("Min Opacity", ref minOpacity, 0.0f, 1.0f))
            {
                Configuration.MinOpacity = minOpacity;
                Configuration.Save();
            }

            var maxOpacity = Configuration.MaxOpacity;
            if (ImGui.SliderFloat("Max Opacity", ref maxOpacity, 0.0f, 1.0f))
            {
                Configuration.MaxOpacity = maxOpacity;
                Configuration.Save();
            }

            ImGui.Unindent();
        }

        // Animation Speed
        if (Configuration.LineStyle == LineStyle.Animated || Configuration.LineStyle == LineStyle.Wavy)
        {
            var animSpeed = Configuration.AnimationSpeed;
            if (ImGui.SliderFloat("Animation Speed", ref animSpeed, 0.1f, 5.0f))
            {
                Configuration.AnimationSpeed = animSpeed;
                Configuration.Save();
            }
        }

        // Particle Settings
        if (Configuration.LineStyle == LineStyle.Particles)
        {
            var enableParticles = Configuration.EnableParticleEffects;
            if (ImGui.Checkbox("Enable Particle Effects", ref enableParticles))
            {
                Configuration.EnableParticleEffects = enableParticles;
                Configuration.Save();
            }

            if (Configuration.EnableParticleEffects)
            {
                ImGui.Indent();
                var particleCount = Configuration.ParticleCount;
                if (ImGui.SliderFloat("Particle Count", ref particleCount, 5.0f, 50.0f))
                {
                    Configuration.ParticleCount = particleCount;
                    Configuration.Save();
                }
                ImGui.Unindent();
            }
        }

        // Custom Line Pattern Settings
        if (Configuration.LineStyle == LineStyle.Dashed || Configuration.LineStyle == LineStyle.Dotted)
        {
            var dashLength = Configuration.DashLength;
            if (ImGui.SliderFloat("Dash Length", ref dashLength, 2.0f, 20.0f))
            {
                Configuration.DashLength = dashLength;
                Configuration.Save();
            }

            var dashSpacing = Configuration.DashSpacing;
            if (ImGui.SliderFloat("Dash Spacing", ref dashSpacing, 1.0f, 15.0f))
            {
                Configuration.DashSpacing = dashSpacing;
                Configuration.Save();
            }
        }

        if (Configuration.LineStyle == LineStyle.Wavy)
        {
            var waveAmplitude = Configuration.WaveAmplitude;
            if (ImGui.SliderFloat("Wave Amplitude", ref waveAmplitude, 1.0f, 20.0f))
            {
                Configuration.WaveAmplitude = waveAmplitude;
                Configuration.Save();
            }

            var waveFrequency = Configuration.WaveFrequency;
            if (ImGui.SliderFloat("Wave Frequency", ref waveFrequency, 0.05f, 1.0f))
            {
                Configuration.WaveFrequency = waveFrequency;
                Configuration.Save();
            }
        }

        // Advanced Color Systems
        ImGui.Spacing();
        ImGui.Separator();
        ImGui.Text("Advanced Color Systems:");

        var useThreatColors = Configuration.UseThreatLevelColors;
        if (ImGui.Checkbox("Use Threat-Level Colors", ref useThreatColors))
        {
            Configuration.UseThreatLevelColors = useThreatColors;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Color enemies based on their calculated threat level:\n" +
                           "• Green: Low threat (tanks, low health enemies)\n" +
                           "• Yellow: Medium threat\n" +
                           "• Orange: High threat (DPS, healers)\n" +
                           "• Red: Critical threat (vulnerable healers/DPS)");
        }

        if (Configuration.UseThreatLevelColors)
        {
            ImGui.Indent();

            var lowThreat = Configuration.LowThreatColor;
            if (ImGui.ColorEdit4("Low Threat", ref lowThreat))
            {
                Configuration.LowThreatColor = lowThreat;
                Configuration.Save();
            }

            var mediumThreat = Configuration.MediumThreatColor;
            if (ImGui.ColorEdit4("Medium Threat", ref mediumThreat))
            {
                Configuration.MediumThreatColor = mediumThreat;
                Configuration.Save();
            }

            var highThreat = Configuration.HighThreatColor;
            if (ImGui.ColorEdit4("High Threat", ref highThreat))
            {
                Configuration.HighThreatColor = highThreat;
                Configuration.Save();
            }

            var criticalThreat = Configuration.CriticalThreatColor;
            if (ImGui.ColorEdit4("Critical Threat", ref criticalThreat))
            {
                Configuration.CriticalThreatColor = criticalThreat;
                Configuration.Save();
            }

            ImGui.Unindent();
        }

        var useHealthColors = Configuration.UseHealthBasedColors;
        if (ImGui.Checkbox("Use Health-Based Colors", ref useHealthColors))
        {
            Configuration.UseHealthBasedColors = useHealthColors;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Color enemies based on their current health percentage");
        }

        if (Configuration.UseHealthBasedColors)
        {
            ImGui.Indent();

            var fullHealth = Configuration.FullHealthColor;
            if (ImGui.ColorEdit4("Full Health", ref fullHealth))
            {
                Configuration.FullHealthColor = fullHealth;
                Configuration.Save();
            }

            var midHealth = Configuration.MidHealthColor;
            if (ImGui.ColorEdit4("Mid Health", ref midHealth))
            {
                Configuration.MidHealthColor = midHealth;
                Configuration.Save();
            }

            var lowHealth = Configuration.LowHealthColor;
            if (ImGui.ColorEdit4("Low Health", ref lowHealth))
            {
                Configuration.LowHealthColor = lowHealth;
                Configuration.Save();
            }

            ImGui.Unindent();
        }

        var useTeamColors = Configuration.UseTeamColors;
        if (ImGui.Checkbox("Use Team Colors", ref useTeamColors))
        {
            Configuration.UseTeamColors = useTeamColors;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Color players based on their Grand Company affiliation");
        }

        if (Configuration.UseTeamColors)
        {
            ImGui.Indent();

            var maelstrom = Configuration.MaelstromColor;
            if (ImGui.ColorEdit4("Maelstrom", ref maelstrom))
            {
                Configuration.MaelstromColor = maelstrom;
                Configuration.Save();
            }

            var adder = Configuration.AdderColor;
            if (ImGui.ColorEdit4("Twin Adder", ref adder))
            {
                Configuration.AdderColor = adder;
                Configuration.Save();
            }

            var flames = Configuration.FlamesColor;
            if (ImGui.ColorEdit4("Immortal Flames", ref flames))
            {
                Configuration.FlamesColor = flames;
                Configuration.Save();
            }

            ImGui.Unindent();
        }
    }

    private void DrawInformationOverlaysTab()
    {
        if (ImGui.BeginTabItem("Info Overlays"))
        {
            ImGui.TextWrapped("Enhanced information displays for better tactical awareness.");
            ImGui.Spacing();

            // Mini Health Bars
            var showHealthBars = Configuration.ShowMiniHealthBars;
            if (ImGui.Checkbox("Show Mini Health Bars", ref showHealthBars))
            {
                Configuration.ShowMiniHealthBars = showHealthBars;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display small health bars above enemy players");
            }

            if (Configuration.ShowMiniHealthBars)
            {
                ImGui.Indent();

                var healthBarWidth = Configuration.HealthBarWidth;
                if (ImGui.SliderFloat("Health Bar Width", ref healthBarWidth, 20f, 80f))
                {
                    Configuration.HealthBarWidth = healthBarWidth;
                    Configuration.Save();
                }

                var healthBarHeight = Configuration.HealthBarHeight;
                if (ImGui.SliderFloat("Health Bar Height", ref healthBarHeight, 4f, 12f))
                {
                    Configuration.HealthBarHeight = healthBarHeight;
                    Configuration.Save();
                }

                ImGui.Unindent();
            }

            ImGui.Spacing();

            // Distance Rings
            var showDistanceRings = Configuration.ShowDistanceRings;
            if (ImGui.Checkbox("Show Distance Rings", ref showDistanceRings))
            {
                Configuration.ShowDistanceRings = showDistanceRings;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display concentric circles showing distance ranges around your character");
            }

            // Enhanced Job Icons
            var showJobIcons = Configuration.ShowEnhancedJobIcons;
            if (ImGui.Checkbox("Show Enhanced Job Icons", ref showJobIcons))
            {
                Configuration.ShowEnhancedJobIcons = showJobIcons;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display role icons next to enemy players");
            }

            if (Configuration.ShowEnhancedJobIcons)
            {
                ImGui.Indent();
                var jobIconSize = Configuration.JobIconSize;
                if (ImGui.SliderFloat("Job Icon Size", ref jobIconSize, 8f, 32f))
                {
                    Configuration.JobIconSize = jobIconSize;
                    Configuration.Save();
                }
                ImGui.Unindent();
            }

            ImGui.Spacing();

            // Threat Meters
            var showThreatMeters = Configuration.ShowThreatMeters;
            if (ImGui.Checkbox("Show Threat Meters", ref showThreatMeters))
            {
                Configuration.ShowThreatMeters = showThreatMeters;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display vertical threat level indicators next to enemies");
            }

            if (Configuration.ShowThreatMeters)
            {
                ImGui.Indent();

                var threatWidth = Configuration.ThreatMeterWidth;
                if (ImGui.SliderFloat("Threat Meter Width", ref threatWidth, 15f, 50f))
                {
                    Configuration.ThreatMeterWidth = threatWidth;
                    Configuration.Save();
                }

                var threatHeight = Configuration.ThreatMeterHeight;
                if (ImGui.SliderFloat("Threat Meter Height", ref threatHeight, 30f, 100f))
                {
                    Configuration.ThreatMeterHeight = threatHeight;
                    Configuration.Save();
                }

                ImGui.Unindent();
            }

            ImGui.Spacing();

            // Cast Bars
            var showCastBars = Configuration.ShowEnemyCastBars;
            if (ImGui.Checkbox("Show Enemy Cast Bars", ref showCastBars))
            {
                Configuration.ShowEnemyCastBars = showCastBars;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display cast progress bars when enemies are casting abilities");
            }

            if (Configuration.ShowEnemyCastBars)
            {
                ImGui.Indent();

                var castBarWidth = Configuration.CastBarWidth;
                if (ImGui.SliderFloat("Cast Bar Width", ref castBarWidth, 40f, 120f))
                {
                    Configuration.CastBarWidth = castBarWidth;
                    Configuration.Save();
                }

                var castBarHeight = Configuration.CastBarHeight;
                if (ImGui.SliderFloat("Cast Bar Height", ref castBarHeight, 4f, 16f))
                {
                    Configuration.CastBarHeight = castBarHeight;
                    Configuration.Save();
                }

                ImGui.Unindent();
            }

            ImGui.Spacing();

            // Status Effect Icons
            var showStatusIcons = Configuration.ShowStatusEffectIcons;
            if (ImGui.Checkbox("Show Status Effect Icons", ref showStatusIcons))
            {
                Configuration.ShowStatusEffectIcons = showStatusIcons;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display status effect icons below enemy players");
            }

            if (Configuration.ShowStatusEffectIcons)
            {
                ImGui.Indent();

                var maxEffects = Configuration.MaxStatusEffectsShown;
                if (ImGui.SliderInt("Max Effects Shown", ref maxEffects, 3, 10))
                {
                    Configuration.MaxStatusEffectsShown = maxEffects;
                    Configuration.Save();
                }

                var statusIconSize = Configuration.StatusIconSize;
                if (ImGui.SliderFloat("Status Icon Size", ref statusIconSize, 8f, 20f))
                {
                    Configuration.StatusIconSize = statusIconSize;
                    Configuration.Save();
                }

                ImGui.Unindent();
            }

            ImGui.Spacing();

            // Cooldown Timers
            var showCooldowns = Configuration.ShowCooldownTimers;
            if (ImGui.Checkbox("Show Cooldown Timers", ref showCooldowns))
            {
                Configuration.ShowCooldownTimers = showCooldowns;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display cooldown timers for important enemy abilities");
            }

            if (Configuration.ShowCooldownTimers)
            {
                ImGui.Indent();
                var importantOnly = Configuration.ShowImportantCooldownsOnly;
                if (ImGui.Checkbox("Important Cooldowns Only", ref importantOnly))
                {
                    Configuration.ShowImportantCooldownsOnly = importantOnly;
                    Configuration.Save();
                }
                ImGui.Unindent();
            }

            ImGui.Spacing();

            // Mini Compass
            var showCompass = Configuration.ShowMiniCompass;
            if (ImGui.Checkbox("Show Mini Compass", ref showCompass))
            {
                Configuration.ShowMiniCompass = showCompass;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display a small compass pointing to the nearest enemy");
            }

            if (Configuration.ShowMiniCompass)
            {
                ImGui.Indent();
                var compassSize = Configuration.CompassSize;
                if (ImGui.SliderFloat("Compass Size", ref compassSize, 15f, 50f))
                {
                    Configuration.CompassSize = compassSize;
                    Configuration.Save();
                }
                ImGui.Unindent();
            }

            ImGui.EndTabItem();
        }
    }

}
