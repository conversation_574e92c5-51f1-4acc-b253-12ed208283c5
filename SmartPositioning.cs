using System;
using System.Collections.Generic;
using System.Numerics;
using ImGuiNET;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public static class SmartPositioning
{
    private static readonly List<Vector2> _occupiedPositions = new();
    
    public static void ClearOccupiedPositions()
    {
        _occupiedPositions.Clear();
    }
    
    public static Vector2 FindAvailablePosition(Vector2 preferredPosition, float radius = 20f, int maxAttempts = 8)
    {
        // Check if preferred position is available
        if (IsPositionAvailable(preferredPosition, radius))
        {
            _occupiedPositions.Add(preferredPosition);
            return preferredPosition;
        }
        
        // Try positions in a spiral pattern around the preferred position
        for (int attempt = 1; attempt <= maxAttempts; attempt++)
        {
            var angleStep = (2 * Math.PI) / (attempt * 4);
            var distance = radius * attempt * 0.5f;
            
            for (int i = 0; i < attempt * 4; i++)
            {
                var angle = i * angleStep;
                var testPosition = preferredPosition + new Vector2(
                    (float)(Math.Cos(angle) * distance),
                    (float)(Math.Sin(angle) * distance)
                );
                
                if (IsPositionAvailable(testPosition, radius))
                {
                    _occupiedPositions.Add(testPosition);
                    return testPosition;
                }
            }
        }
        
        // If no position found, use preferred position anyway
        _occupiedPositions.Add(preferredPosition);
        return preferredPosition;
    }
    
    private static bool IsPositionAvailable(Vector2 position, float radius)
    {
        foreach (var occupied in _occupiedPositions)
        {
            if (Vector2.Distance(position, occupied) < radius)
                return false;
        }
        return true;
    }
    
    public static void DrawScreenEdgeIndicator(ImDrawListPtr drawList, Vector2 playerScreenPos, 
        Vector2 targetWorldPos, Vector2 playerWorldPos, PlayerDisplayData data, Configuration config)
    {
        var screenSize = ImGui.GetIO().DisplaySize;
        var margin = 50f;
        
        // Calculate direction to target
        var worldDirection = targetWorldPos - playerWorldPos;
        var direction2D = Vector2.Normalize(new Vector2(worldDirection.X, worldDirection.Z));
        
        // Find intersection with screen edge
        var edgePosition = GetScreenEdgePosition(playerScreenPos, direction2D, screenSize, margin);
        
        // Draw arrow pointing to off-screen target
        DrawEdgeArrow(drawList, edgePosition, direction2D, data, config);
        
        // Draw distance text
        var distance = Vector2.Distance(targetWorldPos, playerWorldPos);
        var distanceText = $"{distance:F0}y";
        var textSize = ImGui.CalcTextSize(distanceText);
        var textPos = edgePosition + new Vector2(-textSize.X / 2, -textSize.Y - 25);
        
        var textColor = config.GetThemedColor(config.LineColor);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(textColor), distanceText);
    }
    
    private static Vector2 GetScreenEdgePosition(Vector2 center, Vector2 direction, Vector2 screenSize, float margin)
    {
        // Calculate intersection with screen boundaries
        var edgeX = direction.X > 0 ? screenSize.X - margin : margin;
        var edgeY = direction.Y > 0 ? screenSize.Y - margin : margin;
        
        // Calculate intersection points
        var tX = direction.X != 0 ? (edgeX - center.X) / direction.X : float.MaxValue;
        var tY = direction.Y != 0 ? (edgeY - center.Y) / direction.Y : float.MaxValue;
        
        // Use the smaller t value (closer intersection)
        var t = Math.Min(Math.Abs(tX), Math.Abs(tY));
        if (tX < 0) t = Math.Abs(tY);
        if (tY < 0) t = Math.Abs(tX);
        
        var intersection = center + direction * t;
        
        // Clamp to screen bounds with margin
        intersection.X = Math.Clamp(intersection.X, margin, screenSize.X - margin);
        intersection.Y = Math.Clamp(intersection.Y, margin, screenSize.Y - margin);
        
        return intersection;
    }
    
    private static void DrawEdgeArrow(ImDrawListPtr drawList, Vector2 position, Vector2 direction, 
        PlayerDisplayData data, Configuration config)
    {
        var arrowSize = config.ArrowSize;
        var color = config.GetThemedColor(config.LineColor);
        
        // Adjust color based on player data
        if (data.IsLowHealth)
            color = config.LowHealthLineColor;
        else if (data.HasDefensiveBuff)
            color = new Vector4(0.5f, 0.5f, 1.0f, 1.0f);
        
        // Draw arrow background circle
        var bgColor = new Vector4(0.1f, 0.1f, 0.1f, 0.8f);
        drawList.AddCircleFilled(position, arrowSize + 3, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Draw arrow
        var arrowPoints = new Vector2[3];
        var angle = Math.Atan2(direction.Y, direction.X);
        
        arrowPoints[0] = position + new Vector2(
            (float)(Math.Cos(angle) * arrowSize),
            (float)(Math.Sin(angle) * arrowSize)
        );
        arrowPoints[1] = position + new Vector2(
            (float)(Math.Cos(angle + Math.PI * 0.75) * arrowSize * 0.6),
            (float)(Math.Sin(angle + Math.PI * 0.75) * arrowSize * 0.6)
        );
        arrowPoints[2] = position + new Vector2(
            (float)(Math.Cos(angle - Math.PI * 0.75) * arrowSize * 0.6),
            (float)(Math.Sin(angle - Math.PI * 0.75) * arrowSize * 0.6)
        );
        
        // Fill arrow
        drawList.AddTriangleFilled(arrowPoints[0], arrowPoints[1], arrowPoints[2], 
            ImGui.ColorConvertFloat4ToU32(color));
        
        // Draw role indicator
        var roleChar = data.Role.ToString()[0].ToString();
        var textSize = ImGui.CalcTextSize(roleChar);
        var textPos = new Vector2(position.X - textSize.X / 2, position.Y - textSize.Y / 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), roleChar);
    }
    
    public static void DrawRadarView(ImDrawListPtr drawList, Vector2 center, float radius, 
        IPlayerCharacter localPlayer, List<IPlayerCharacter> players, Configuration config)
    {
        // Radar background
        var bgColor = new Vector4(0.0f, 0.0f, 0.0f, 0.7f);
        drawList.AddCircleFilled(center, radius, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Radar border
        var borderColor = new Vector4(0.5f, 0.5f, 0.5f, 1.0f);
        drawList.AddCircle(center, radius, ImGui.ColorConvertFloat4ToU32(borderColor), 32, 2f);
        
        // Range rings
        for (int i = 1; i <= 3; i++)
        {
            var ringRadius = radius * i / 3f;
            var ringColor = new Vector4(0.3f, 0.3f, 0.3f, 0.5f);
            drawList.AddCircle(center, ringRadius, ImGui.ColorConvertFloat4ToU32(ringColor), 32, 1f);
        }
        
        // Player position (center)
        var playerColor = new Vector4(1.0f, 1.0f, 1.0f, 1.0f);
        drawList.AddCircleFilled(center, 3f, ImGui.ColorConvertFloat4ToU32(playerColor));
        
        // Other players
        var maxRange = config.MaxDistance;
        foreach (var player in players)
        {
            var worldDistance = Vector3.Distance(localPlayer.Position, player.Position);
            if (worldDistance > maxRange) continue;
            
            // Calculate relative position
            var relativePos = player.Position - localPlayer.Position;
            var radarPos = center + new Vector2(relativePos.X, relativePos.Z) * (radius / maxRange);
            
            // Clamp to radar bounds
            var distanceFromCenter = Vector2.Distance(center, radarPos);
            if (distanceFromCenter > radius - 5)
            {
                var direction = Vector2.Normalize(radarPos - center);
                radarPos = center + direction * (radius - 5);
            }
            
            // Determine player color and size
            var isAlly = IsPlayerAlly(localPlayer, player);
            var playerData = GatherPlayerData(player, isAlly);
            var dotColor = GetRadarDotColor(playerData, config);
            var dotSize = GetRadarDotSize(playerData);
            
            // Draw player dot
            drawList.AddCircleFilled(radarPos, dotSize, ImGui.ColorConvertFloat4ToU32(dotColor));
            
            // Draw role indicator for enemies
            if (!isAlly && config.ShowPlayerJobs)
            {
                var roleChar = playerData.Role.ToString()[0].ToString();
                var textSize = ImGui.CalcTextSize(roleChar);
                var textPos = new Vector2(radarPos.X - textSize.X / 2, radarPos.Y - textSize.Y / 2);
                drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), roleChar);
            }
        }
        
        // Radar label
        var radarLabel = "RADAR";
        var labelSize = ImGui.CalcTextSize(radarLabel);
        var labelPos = new Vector2(center.X - labelSize.X / 2, center.Y - radius - labelSize.Y - 5);
        drawList.AddText(labelPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), radarLabel);
    }
    
    private static Vector4 GetRadarDotColor(PlayerDisplayData data, Configuration config)
    {
        if (data.IsAlly)
            return config.AllyLineColor;
        
        if (data.IsLowHealth)
            return config.LowHealthLineColor;
        
        if (config.UseThreatLevelColors)
        {
            var threatLevel = config.CalculateThreatLevel(data);
            return config.GetThreatLevelColor(threatLevel);
        }
        
        if (config.ColorCodeByRole)
            return JobHelper.GetRoleColor(data.Role);
        
        return config.LineColor;
    }
    
    private static float GetRadarDotSize(PlayerDisplayData data)
    {
        var baseSize = 2f;
        
        if (data.IsLowHealth) baseSize += 1f;
        if (data.HasDefensiveBuff) baseSize += 0.5f;
        if (data.IsCrowdControlled) baseSize += 1f;
        
        return Math.Clamp(baseSize, 1f, 5f);
    }
    
    public static void DrawLayeredDepthIndicators(ImDrawListPtr drawList, Vector2 position, 
        float distance, PlayerDisplayData data, Configuration config)
    {
        // Calculate depth layers based on distance
        var layer = distance switch
        {
            < 10f => 0,  // Close
            < 25f => 1,  // Medium
            < 50f => 2,  // Far
            _ => 3       // Very far
        };
        
        // Adjust visual properties based on layer
        var alpha = layer switch
        {
            0 => 1.0f,
            1 => 0.8f,
            2 => 0.6f,
            _ => 0.4f
        };
        
        var size = layer switch
        {
            0 => 1.2f,
            1 => 1.0f,
            2 => 0.8f,
            _ => 0.6f
        };
        
        // Draw depth indicator
        var depthColor = new Vector4(1f, 1f, 1f, alpha * 0.5f);
        var indicatorSize = 8f * size;
        
        drawList.AddCircle(position, indicatorSize, ImGui.ColorConvertFloat4ToU32(depthColor), 16, 1f);
        
        // Draw layer number
        var layerText = (layer + 1).ToString();
        var textSize = ImGui.CalcTextSize(layerText);
        var textPos = new Vector2(position.X - textSize.X / 2, position.Y - textSize.Y / 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(depthColor), layerText);
    }
    
    // Helper methods that would need to be implemented or imported
    private static bool IsPlayerAlly(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer)
    {
        // This would need proper implementation based on game data
        return false; // Placeholder
    }
    
    private static PlayerDisplayData GatherPlayerData(IPlayerCharacter player, bool isAlly)
    {
        // This would need proper implementation based on game data
        return new PlayerDisplayData(PlayerRole.Unknown, 100f, isAlly, false, false, false, false, false, false);
    }
}
