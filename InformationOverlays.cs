using System;
using System.Collections.Generic;
using System.Numerics;
using ImGuiNET;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public static class InformationOverlays
{
    public static void DrawMiniHealthBar(ImDrawListPtr drawList, Vector2 position, float healthPercentage, 
        Vector4 color, float width = 40f, float height = 6f)
    {
        var barMin = new Vector2(position.X - width / 2, position.Y - height / 2);
        var barMax = new Vector2(position.X + width / 2, position.Y + height / 2);
        
        // Background
        var bgColor = new Vector4(0.2f, 0.2f, 0.2f, 0.8f);
        drawList.AddRectFilled(barMin, barMax, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Health bar
        var healthWidth = width * Math.Clamp(healthPercentage / 100f, 0f, 1f);
        var healthMax = new Vector2(barMin.X + healthWidth, barMax.Y);
        
        // Color based on health percentage
        var healthColor = healthPercentage switch
        {
            > 66f => new Vector4(0.0f, 1.0f, 0.0f, 0.9f), // Green
            > 33f => new Vector4(1.0f, 1.0f, 0.0f, 0.9f), // Yellow
            _ => new Vector4(1.0f, 0.0f, 0.0f, 0.9f)       // Red
        };
        
        if (healthWidth > 0)
        {
            drawList.AddRectFilled(barMin, healthMax, ImGui.ColorConvertFloat4ToU32(healthColor));
        }
        
        // Border
        drawList.AddRect(barMin, barMax, ImGui.ColorConvertFloat4ToU32(Vector4.One), 0f, 0, 1f);
        
        // Health percentage text
        var healthText = $"{healthPercentage:F0}%";
        var textSize = ImGui.CalcTextSize(healthText);
        var textPos = new Vector2(position.X - textSize.X / 2, position.Y + height / 2 + 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), healthText);
    }
    
    public static void DrawDistanceRings(ImDrawListPtr drawList, Vector2 center, Configuration config)
    {
        if (!config.ShowDistanceRings) return;
        
        var rings = new[] { 10f, 20f, 30f, 50f }; // Distance rings in yalms
        var screenScale = 2f; // Approximate screen scale factor
        
        foreach (var distance in rings)
        {
            var radius = distance * screenScale;
            var alpha = 0.3f - (distance / 50f * 0.2f); // Fade with distance
            var ringColor = new Vector4(1f, 1f, 1f, alpha);
            
            drawList.AddCircle(center, radius, ImGui.ColorConvertFloat4ToU32(ringColor), 64, 1f);
            
            // Distance label
            var label = $"{distance}y";
            var labelPos = new Vector2(center.X + radius - 15, center.Y - 8);
            drawList.AddText(labelPos, ImGui.ColorConvertFloat4ToU32(ringColor), label);
        }
    }
    
    public static void DrawCooldownTimer(ImDrawListPtr drawList, Vector2 position, string abilityName, 
        float remainingTime, Vector4 color)
    {
        if (remainingTime <= 0) return;
        
        var radius = 12f;
        var center = position;
        
        // Background circle
        var bgColor = new Vector4(0.2f, 0.2f, 0.2f, 0.8f);
        drawList.AddCircleFilled(center, radius, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Cooldown arc (clockwise from top)
        var progress = Math.Clamp(remainingTime / 30f, 0f, 1f); // Assume 30s max cooldown
        var startAngle = -Math.PI / 2; // Start from top
        var endAngle = startAngle + (2 * Math.PI * progress);
        
        // Draw arc segments
        var segments = 32;
        var angleStep = (endAngle - startAngle) / segments;
        
        for (int i = 0; i < segments; i++)
        {
            var angle1 = startAngle + (i * angleStep);
            var angle2 = startAngle + ((i + 1) * angleStep);
            
            var p1 = center + new Vector2((float)Math.Cos(angle1) * radius, (float)Math.Sin(angle1) * radius);
            var p2 = center + new Vector2((float)Math.Cos(angle2) * radius, (float)Math.Sin(angle2) * radius);
            
            drawList.AddLine(center, p1, ImGui.ColorConvertFloat4ToU32(color), 2f);
        }
        
        // Timer text
        var timeText = $"{remainingTime:F1}s";
        var textSize = ImGui.CalcTextSize(timeText);
        var textPos = new Vector2(center.X - textSize.X / 2, center.Y - textSize.Y / 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), timeText);
        
        // Ability name below
        var nameSize = ImGui.CalcTextSize(abilityName);
        var namePos = new Vector2(center.X - nameSize.X / 2, center.Y + radius + 2);
        drawList.AddText(namePos, ImGui.ColorConvertFloat4ToU32(color), abilityName);
    }
    
    public static void DrawCastBar(ImDrawListPtr drawList, Vector2 position, string spellName, 
        float castProgress, Vector4 color, float width = 80f, float height = 8f)
    {
        var barMin = new Vector2(position.X - width / 2, position.Y - height / 2);
        var barMax = new Vector2(position.X + width / 2, position.Y + height / 2);
        
        // Background
        var bgColor = new Vector4(0.1f, 0.1f, 0.1f, 0.9f);
        drawList.AddRectFilled(barMin, barMax, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Cast progress
        var progressWidth = width * Math.Clamp(castProgress, 0f, 1f);
        var progressMax = new Vector2(barMin.X + progressWidth, barMax.Y);
        
        if (progressWidth > 0)
        {
            drawList.AddRectFilled(barMin, progressMax, ImGui.ColorConvertFloat4ToU32(color));
        }
        
        // Border
        drawList.AddRect(barMin, barMax, ImGui.ColorConvertFloat4ToU32(Vector4.One), 0f, 0, 1f);
        
        // Spell name
        var textSize = ImGui.CalcTextSize(spellName);
        var textPos = new Vector2(position.X - textSize.X / 2, position.Y - height / 2 - textSize.Y - 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), spellName);
        
        // Progress percentage
        var progressText = $"{castProgress * 100:F0}%";
        var progressSize = ImGui.CalcTextSize(progressText);
        var progressPos = new Vector2(position.X - progressSize.X / 2, position.Y - progressSize.Y / 2);
        drawList.AddText(progressPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), progressText);
    }
    
    public static void DrawThreatMeter(ImDrawListPtr drawList, Vector2 position, float threatLevel, 
        Vector4 color, float width = 30f, float height = 60f)
    {
        var barMin = new Vector2(position.X - width / 2, position.Y - height / 2);
        var barMax = new Vector2(position.X + width / 2, position.Y + height / 2);
        
        // Background
        var bgColor = new Vector4(0.2f, 0.2f, 0.2f, 0.8f);
        drawList.AddRectFilled(barMin, barMax, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Threat level
        var threatHeight = height * Math.Clamp(threatLevel, 0f, 1f);
        var threatMin = new Vector2(barMin.X, barMax.Y - threatHeight);
        
        // Color based on threat level
        var threatColor = threatLevel switch
        {
            < 0.25f => new Vector4(0.0f, 1.0f, 0.0f, 0.8f), // Green
            < 0.5f => new Vector4(1.0f, 1.0f, 0.0f, 0.8f),  // Yellow
            < 0.75f => new Vector4(1.0f, 0.5f, 0.0f, 0.8f), // Orange
            _ => new Vector4(1.0f, 0.0f, 0.0f, 0.8f)         // Red
        };
        
        if (threatHeight > 0)
        {
            drawList.AddRectFilled(threatMin, barMax, ImGui.ColorConvertFloat4ToU32(threatColor));
        }
        
        // Border
        drawList.AddRect(barMin, barMax, ImGui.ColorConvertFloat4ToU32(Vector4.One), 0f, 0, 1f);
        
        // Threat level text
        var threatText = $"{threatLevel * 100:F0}%";
        var textSize = ImGui.CalcTextSize(threatText);
        var textPos = new Vector2(position.X - textSize.X / 2, position.Y + height / 2 + 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), threatText);
    }
    
    public static void DrawJobIcon(ImDrawListPtr drawList, Vector2 position, PlayerRole role, 
        Vector4 color, float size = 16f)
    {
        var iconText = role switch
        {
            PlayerRole.Tank => "🛡",
            PlayerRole.Healer => "✚",
            PlayerRole.MeleeDPS => "⚔",
            PlayerRole.RangedDPS => "🏹",
            PlayerRole.MagicalDPS => "🔮",
            _ => "?"
        };
        
        // Background circle
        var bgColor = new Vector4(0.2f, 0.2f, 0.2f, 0.8f);
        drawList.AddCircleFilled(position, size / 2, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Role color circle
        var roleColor = JobHelper.GetRoleColor(role);
        drawList.AddCircleFilled(position, size / 2 - 1, ImGui.ColorConvertFloat4ToU32(roleColor));
        
        // Icon text
        var textSize = ImGui.CalcTextSize(iconText);
        var textPos = new Vector2(position.X - textSize.X / 2, position.Y - textSize.Y / 2);
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), iconText);
    }
    
    public static void DrawStatusEffectIcons(ImDrawListPtr drawList, Vector2 position,
        List<StatusInfo> effects, float iconSize = 12f)
    {
        var spacing = iconSize + 2f;
        var startX = position.X - (effects.Count * spacing) / 2;
        
        for (int i = 0; i < effects.Count && i < 6; i++) // Limit to 6 effects
        {
            var effect = effects[i];
            var iconPos = new Vector2(startX + (i * spacing), position.Y);
            
            // Effect background
            var bgColor = effect.IsNegative 
                ? new Vector4(0.8f, 0.2f, 0.2f, 0.8f) 
                : new Vector4(0.2f, 0.8f, 0.2f, 0.8f);
            
            drawList.AddCircleFilled(iconPos, iconSize / 2, ImGui.ColorConvertFloat4ToU32(bgColor));
            
            // Effect icon (simplified)
            var iconChar = effect.IsNegative ? "!" : "+";
            var textSize = ImGui.CalcTextSize(iconChar);
            var textPos = new Vector2(iconPos.X - textSize.X / 2, iconPos.Y - textSize.Y / 2);
            drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), iconChar);
            
            // Duration text
            if (effect.RemainingTime.HasValue)
            {
                var durationText = $"{effect.RemainingTime.Value:F0}";
                var durationSize = ImGui.CalcTextSize(durationText);
                var durationPos = new Vector2(iconPos.X - durationSize.X / 2, iconPos.Y + iconSize / 2 + 1);
                drawList.AddText(durationPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), durationText);
            }
        }
    }
    
    public static void DrawCompass(ImDrawListPtr drawList, Vector2 center, Vector2 targetDirection, 
        float radius = 25f)
    {
        // Compass background
        var bgColor = new Vector4(0.1f, 0.1f, 0.1f, 0.8f);
        drawList.AddCircleFilled(center, radius, ImGui.ColorConvertFloat4ToU32(bgColor));
        
        // Compass border
        drawList.AddCircle(center, radius, ImGui.ColorConvertFloat4ToU32(Vector4.One), 32, 2f);
        
        // North indicator
        var northPos = center + new Vector2(0, -radius + 5);
        drawList.AddText(northPos, ImGui.ColorConvertFloat4ToU32(Vector4.One), "N");
        
        // Target direction arrow
        var arrowLength = radius - 8;
        var arrowEnd = center + Vector2.Normalize(targetDirection) * arrowLength;
        
        drawList.AddLine(center, arrowEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(1, 0, 0, 1)), 3f);
        
        // Arrow head
        var arrowAngle = Math.Atan2(targetDirection.Y, targetDirection.X);
        var arrowHead1 = arrowEnd + new Vector2(
            (float)Math.Cos(arrowAngle + Math.PI * 0.8) * 8,
            (float)Math.Sin(arrowAngle + Math.PI * 0.8) * 8
        );
        var arrowHead2 = arrowEnd + new Vector2(
            (float)Math.Cos(arrowAngle - Math.PI * 0.8) * 8,
            (float)Math.Sin(arrowAngle - Math.PI * 0.8) * 8
        );
        
        drawList.AddLine(arrowEnd, arrowHead1, ImGui.ColorConvertFloat4ToU32(new Vector4(1, 0, 0, 1)), 2f);
        drawList.AddLine(arrowEnd, arrowHead2, ImGui.ColorConvertFloat4ToU32(new Vector4(1, 0, 0, 1)), 2f);
    }
}
