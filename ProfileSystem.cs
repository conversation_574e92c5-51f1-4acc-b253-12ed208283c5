using System;
using System.Collections.Generic;
using System.IO;
using System.Numerics;
using System.Text.Json;
using Dalamud.Plugin;

namespace PvPLinePlugin;

[Serializable]
public class AppearanceProfile
{
    public string Name { get; set; } = "Default";
    public string Description { get; set; } = "";
    public DateTime Created { get; set; } = DateTime.Now;
    public DateTime LastModified { get; set; } = DateTime.Now;
    
    // Visual Settings
    public IndicatorType IndicatorType { get; set; } = IndicatorType.Lines;
    public LineStyle LineStyle { get; set; } = LineStyle.Solid;
    public ColorTheme ColorTheme { get; set; } = ColorTheme.Default;
    public AnimationType AnimationType { get; set; } = AnimationType.None;
    
    // Colors
    public Vector4 LineColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
    public Vector4 AllyLineColor { get; set; } = new Vector4(0.0f, 1.0f, 0.0f, 0.8f);
    public Vector4 LowHealthLineColor { get; set; } = new Vector4(1.0f, 0.8f, 0.0f, 1.0f);
    
    // Sizes and Thickness
    public float LineThickness { get; set; } = 2.0f;
    public float IconSize { get; set; } = 20.0f;
    public float ArrowSize { get; set; } = 15.0f;
    
    // Effects
    public bool Use3DEffects { get; set; } = false;
    public float GlowIntensity { get; set; } = 1.0f;
    public bool UseGradientLines { get; set; } = false;
    public Vector4 GradientEndColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 0.3f);
    
    // Animation Settings
    public float AnimationSpeed { get; set; } = 1.0f;
    public bool EnableParticleEffects { get; set; } = false;
    public float ParticleCount { get; set; } = 10.0f;
    
    // Advanced Color Systems
    public bool UseThreatLevelColors { get; set; } = false;
    public bool UseHealthBasedColors { get; set; } = false;
    public bool UseTeamColors { get; set; } = false;
    
    // Information Overlays
    public bool ShowMiniHealthBars { get; set; } = false;
    public bool ShowEnhancedJobIcons { get; set; } = false;
    public bool ShowThreatMeters { get; set; } = false;
    public bool ShowDistanceRings { get; set; } = false;
    
    // Smart Positioning
    public bool ShowScreenEdgeIndicators { get; set; } = false;
    public bool ShowRadarView { get; set; } = false;
    public bool UseCollisionAvoidance { get; set; } = true;
    public bool GroupNearbyIndicators { get; set; } = false;
    
    public void ApplyToConfiguration(Configuration config)
    {
        config.IndicatorType = IndicatorType;
        config.LineStyle = LineStyle;
        config.ColorTheme = ColorTheme;
        config.AnimationType = AnimationType;
        
        config.LineColor = LineColor;
        config.AllyLineColor = AllyLineColor;
        config.LowHealthLineColor = LowHealthLineColor;
        
        config.LineThickness = LineThickness;
        config.IconSize = IconSize;
        config.ArrowSize = ArrowSize;
        
        config.Use3DEffects = Use3DEffects;
        config.GlowIntensity = GlowIntensity;
        config.UseGradientLines = UseGradientLines;
        config.GradientEndColor = GradientEndColor;
        
        config.AnimationSpeed = AnimationSpeed;
        config.EnableParticleEffects = EnableParticleEffects;
        config.ParticleCount = ParticleCount;
        
        config.UseThreatLevelColors = UseThreatLevelColors;
        config.UseHealthBasedColors = UseHealthBasedColors;
        config.UseTeamColors = UseTeamColors;
        
        config.ShowMiniHealthBars = ShowMiniHealthBars;
        config.ShowEnhancedJobIcons = ShowEnhancedJobIcons;
        config.ShowThreatMeters = ShowThreatMeters;
        config.ShowDistanceRings = ShowDistanceRings;
        
        config.ShowScreenEdgeIndicators = ShowScreenEdgeIndicators;
        config.ShowRadarView = ShowRadarView;
        config.UseCollisionAvoidance = UseCollisionAvoidance;
        config.GroupNearbyIndicators = GroupNearbyIndicators;
        
        LastModified = DateTime.Now;
    }
    
    public static AppearanceProfile FromConfiguration(Configuration config, string name = "Custom Profile")
    {
        return new AppearanceProfile
        {
            Name = name,
            Description = $"Profile created on {DateTime.Now:yyyy-MM-dd HH:mm}",
            
            IndicatorType = config.IndicatorType,
            LineStyle = config.LineStyle,
            ColorTheme = config.ColorTheme,
            AnimationType = config.AnimationType,
            
            LineColor = config.LineColor,
            AllyLineColor = config.AllyLineColor,
            LowHealthLineColor = config.LowHealthLineColor,
            
            LineThickness = config.LineThickness,
            IconSize = config.IconSize,
            ArrowSize = config.ArrowSize,
            
            Use3DEffects = config.Use3DEffects,
            GlowIntensity = config.GlowIntensity,
            UseGradientLines = config.UseGradientLines,
            GradientEndColor = config.GradientEndColor,
            
            AnimationSpeed = config.AnimationSpeed,
            EnableParticleEffects = config.EnableParticleEffects,
            ParticleCount = config.ParticleCount,
            
            UseThreatLevelColors = config.UseThreatLevelColors,
            UseHealthBasedColors = config.UseHealthBasedColors,
            UseTeamColors = config.UseTeamColors,
            
            ShowMiniHealthBars = config.ShowMiniHealthBars,
            ShowEnhancedJobIcons = config.ShowEnhancedJobIcons,
            ShowThreatMeters = config.ShowThreatMeters,
            ShowDistanceRings = config.ShowDistanceRings,
            
            ShowScreenEdgeIndicators = config.ShowScreenEdgeIndicators,
            ShowRadarView = config.ShowRadarView,
            UseCollisionAvoidance = config.UseCollisionAvoidance,
            GroupNearbyIndicators = config.GroupNearbyIndicators
        };
    }
}

public class ProfileManager
{
    private readonly string _profilesDirectory;
    private readonly Dictionary<string, AppearanceProfile> _profiles = new();
    
    public IReadOnlyDictionary<string, AppearanceProfile> Profiles => _profiles;
    
    public ProfileManager(IDalamudPluginInterface pluginInterface)
    {
        _profilesDirectory = Path.Combine(pluginInterface.ConfigDirectory.FullName, "Profiles");
        Directory.CreateDirectory(_profilesDirectory);
        LoadProfiles();
        CreateDefaultProfiles();
    }
    
    private void LoadProfiles()
    {
        try
        {
            foreach (var file in Directory.GetFiles(_profilesDirectory, "*.json"))
            {
                var json = File.ReadAllText(file);
                var profile = JsonSerializer.Deserialize<AppearanceProfile>(json);
                if (profile != null)
                {
                    _profiles[profile.Name] = profile;
                }
            }
        }
        catch (Exception ex)
        {
            // Log error but don't crash
            Console.WriteLine($"Error loading profiles: {ex.Message}");
        }
    }
    
    private void CreateDefaultProfiles()
    {
        // Minimal Profile
        if (!_profiles.ContainsKey("Minimal"))
        {
            var minimal = new AppearanceProfile
            {
                Name = "Minimal",
                Description = "Clean, minimal appearance with basic lines",
                LineStyle = LineStyle.Solid,
                ColorTheme = ColorTheme.Default,
                AnimationType = AnimationType.None,
                LineThickness = 1.5f,
                Use3DEffects = false,
                ShowMiniHealthBars = false,
                ShowEnhancedJobIcons = false,
                UseCollisionAvoidance = true
            };
            SaveProfile(minimal);
        }
        
        // High Visibility Profile
        if (!_profiles.ContainsKey("High Visibility"))
        {
            var highVis = new AppearanceProfile
            {
                Name = "High Visibility",
                Description = "Maximum visibility with bright colors and effects",
                LineStyle = LineStyle.Gradient,
                ColorTheme = ColorTheme.Neon,
                AnimationType = AnimationType.Glow,
                LineThickness = 4.0f,
                Use3DEffects = true,
                GlowIntensity = 2.0f,
                UseThreatLevelColors = true,
                ShowMiniHealthBars = true,
                ShowEnhancedJobIcons = true,
                ShowThreatMeters = true,
                UseCollisionAvoidance = true
            };
            SaveProfile(highVis);
        }
        
        // Tactical Profile
        if (!_profiles.ContainsKey("Tactical"))
        {
            var tactical = new AppearanceProfile
            {
                Name = "Tactical",
                Description = "Information-rich setup for competitive play",
                LineStyle = LineStyle.Solid,
                ColorTheme = ColorTheme.HighContrast,
                AnimationType = AnimationType.Pulse,
                UseThreatLevelColors = true,
                UseHealthBasedColors = true,
                ShowMiniHealthBars = true,
                ShowEnhancedJobIcons = true,
                ShowThreatMeters = true,
                ShowDistanceRings = true,
                ShowScreenEdgeIndicators = true,
                ShowRadarView = true,
                UseCollisionAvoidance = true,
                GroupNearbyIndicators = true
            };
            SaveProfile(tactical);
        }
        
        // Aesthetic Profile
        if (!_profiles.ContainsKey("Aesthetic"))
        {
            var aesthetic = new AppearanceProfile
            {
                Name = "Aesthetic",
                Description = "Beautiful, animated appearance with particle effects",
                LineStyle = LineStyle.Particles,
                ColorTheme = ColorTheme.Cyberpunk,
                AnimationType = AnimationType.Sparkle,
                LineThickness = 2.5f,
                Use3DEffects = true,
                GlowIntensity = 1.5f,
                UseGradientLines = true,
                EnableParticleEffects = true,
                ParticleCount = 15.0f,
                AnimationSpeed = 1.5f,
                ShowMiniHealthBars = true,
                UseCollisionAvoidance = true
            };
            SaveProfile(aesthetic);
        }
    }
    
    public bool SaveProfile(AppearanceProfile profile)
    {
        try
        {
            var filePath = Path.Combine(_profilesDirectory, $"{profile.Name}.json");
            var json = JsonSerializer.Serialize(profile, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(filePath, json);
            _profiles[profile.Name] = profile;
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving profile: {ex.Message}");
            return false;
        }
    }
    
    public bool DeleteProfile(string name)
    {
        try
        {
            var filePath = Path.Combine(_profilesDirectory, $"{name}.json");
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            _profiles.Remove(name);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting profile: {ex.Message}");
            return false;
        }
    }
    
    public AppearanceProfile? GetProfile(string name)
    {
        return _profiles.TryGetValue(name, out var profile) ? profile : null;
    }
    
    public bool RenameProfile(string oldName, string newName)
    {
        if (!_profiles.TryGetValue(oldName, out var profile) || _profiles.ContainsKey(newName))
            return false;
        
        try
        {
            // Delete old file
            var oldPath = Path.Combine(_profilesDirectory, $"{oldName}.json");
            if (File.Exists(oldPath))
            {
                File.Delete(oldPath);
            }
            
            // Update profile name and save
            profile.Name = newName;
            profile.LastModified = DateTime.Now;
            SaveProfile(profile);
            
            // Remove old entry
            _profiles.Remove(oldName);
            
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error renaming profile: {ex.Message}");
            return false;
        }
    }
}
