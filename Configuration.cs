using Dalamud.Configuration;
using Dalamud.Plugin;
using System;
using System.Collections.Generic;
using System.Numerics;

namespace PvPLinePlugin;

public enum IndicatorType
{
    Lines,
    Outlines,
    Nameplates,
    Icons,
    DirectionalArrows,
    HealthBars,
    Combination
}

public enum LineStyle
{
    Solid,
    Dashed,
    Dotted,
    Gradient,
    Animated,
    Lightning,
    Wavy,
    Particles
}

public enum ColorTheme
{
    Default,
    Neon,
    Pastel,
    HighContrast,
    ColorblindFriendly,
    Cyberpunk,
    Nature,
    Custom
}

public enum AnimationType
{
    None,
    Pulse,
    Flow,
    Breathe,
    Sparkle,
    Glow,
    Ripple
}

[Serializable]
public class Configuration : IPluginConfiguration
{
    public int Version { get; set; } = 0;
    public bool Enabled { get; set; } = true;

    #region Visual Indicator Settings
    private IndicatorType _indicatorType = IndicatorType.Lines;
    private Vector4 _lineColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
    private float _lineThickness = 2.0f;
    private float _outlineThickness = 3.0f;
    private float _iconSize = 20.0f;
    private float _arrowSize = 15.0f;

    public IndicatorType IndicatorType
    {
        get => _indicatorType;
        set => _indicatorType = value;
    }

    public Vector4 LineColor
    {
        get => _lineColor;
        set => _lineColor = ValidateColor(value);
    }

    public float LineThickness
    {
        get => _lineThickness;
        set => _lineThickness = Math.Clamp(value, 1.0f, 15.0f);
    }

    public bool ColorCodeByRole { get; set; } = true;

    // Alternative indicator settings
    public float OutlineThickness
    {
        get => _outlineThickness;
        set => _outlineThickness = Math.Clamp(value, 1.0f, 15.0f);
    }

    public float IconSize
    {
        get => _iconSize;
        set => _iconSize = Math.Clamp(value, 5.0f, 100.0f);
    }

    public float ArrowSize
    {
        get => _arrowSize;
        set => _arrowSize = Math.Clamp(value, 5.0f, 50.0f);
    }

    public bool ShowDirectionalArrows { get; set; } = true;
    public bool PulseIndicators { get; set; } = false;

    // Enhanced Visual Styles
    public LineStyle LineStyle { get; set; } = LineStyle.Solid;
    public ColorTheme ColorTheme { get; set; } = ColorTheme.Default;
    public AnimationType AnimationType { get; set; } = AnimationType.None;

    // Gradient and 3D Effects
    public bool UseGradientLines { get; set; } = false;
    public Vector4 GradientEndColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 0.3f);
    public bool Use3DEffects { get; set; } = false;
    public float GlowIntensity { get; set; } = 1.0f;
    public float ShadowOffset { get; set; } = 2.0f;

    // Transparency and Fading
    public bool FadeWithDistance { get; set; } = false;
    public float MinOpacity { get; set; } = 0.3f;
    public float MaxOpacity { get; set; } = 1.0f;
    public bool FadeOutOfSight { get; set; } = false;

    // Animation Settings
    public float AnimationSpeed { get; set; } = 1.0f;
    public float ParticleCount { get; set; } = 10.0f;
    public bool EnableParticleEffects { get; set; } = false;

    // Custom Line Patterns
    public float DashLength { get; set; } = 10.0f;
    public float DashSpacing { get; set; } = 5.0f;
    public float WaveAmplitude { get; set; } = 5.0f;
    public float WaveFrequency { get; set; } = 0.1f;

    // Threat-Level Coloring
    public bool UseThreatLevelColors { get; set; } = false;
    public Vector4 LowThreatColor { get; set; } = new Vector4(0.0f, 1.0f, 0.0f, 1.0f); // Green
    public Vector4 MediumThreatColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f); // Yellow
    public Vector4 HighThreatColor { get; set; } = new Vector4(1.0f, 0.5f, 0.0f, 1.0f); // Orange
    public Vector4 CriticalThreatColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Red

    // Dynamic Health-Based Colors
    public bool UseHealthBasedColors { get; set; } = false;
    public Vector4 FullHealthColor { get; set; } = new Vector4(0.0f, 1.0f, 0.0f, 1.0f);
    public Vector4 MidHealthColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f);
    public Vector4 LowHealthColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);

    // Team-Based Colors
    public bool UseTeamColors { get; set; } = false;
    public Vector4 MaelstromColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Red
    public Vector4 AdderColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f); // Yellow
    public Vector4 FlamesColor { get; set; } = new Vector4(0.0f, 0.0f, 1.0f, 1.0f); // Blue
    #endregion

    #region Information Overlays
    // Mini Health Bars
    public bool ShowMiniHealthBars { get; set; } = false;
    public float HealthBarWidth { get; set; } = 40f;
    public float HealthBarHeight { get; set; } = 6f;

    // Distance Rings
    public bool ShowDistanceRings { get; set; } = false;
    public bool ShowDistanceLabels { get; set; } = true;

    // Cooldown Timers
    public bool ShowCooldownTimers { get; set; } = false;
    public bool ShowImportantCooldownsOnly { get; set; } = true;

    // Cast Bars
    public bool ShowEnemyCastBars { get; set; } = false;
    public float CastBarWidth { get; set; } = 80f;
    public float CastBarHeight { get; set; } = 8f;

    // Threat Meters
    public bool ShowThreatMeters { get; set; } = false;
    public float ThreatMeterWidth { get; set; } = 30f;
    public float ThreatMeterHeight { get; set; } = 60f;

    // Enhanced Job Icons
    public bool ShowEnhancedJobIcons { get; set; } = false;
    public float JobIconSize { get; set; } = 16f;

    // Status Effect Icons
    public bool ShowStatusEffectIcons { get; set; } = false;
    public int MaxStatusEffectsShown { get; set; } = 6;
    public float StatusIconSize { get; set; } = 12f;

    // Compass/Radar
    public bool ShowMiniCompass { get; set; } = false;
    public float CompassSize { get; set; } = 25f;
    public Vector2 CompassPosition { get; set; } = new Vector2(100, 100);
    #endregion

    #region Distance Settings
    private float _maxDistance = 50.0f;
    private float _playerListMaxDistance = 100.0f;

    public float MaxDistance
    {
        get => _maxDistance;
        set => _maxDistance = Math.Clamp(value, 5.0f, 200.0f);
    }

    public bool ShowDistance { get; set; } = true;
    #endregion

    #region PvP Settings
    public bool OnlyInPvP { get; set; } = true;
    public bool ShowInCombatOnly { get; set; } = false;
    #endregion

    #region Player Information
    public bool ShowPlayerNames { get; set; } = false;
    public bool ShowPlayerJobs { get; set; } = true;
    public bool ShowJobIcons { get; set; } = false;
    public bool ShowStatusEffects { get; set; } = true;
    public bool ShowOnlyImportantStatus { get; set; } = true;
    public bool ShowDefensiveBuffs { get; set; } = true;
    #endregion

    #region Low Health Settings
    public bool ShowLowHealthIndicator { get; set; } = true;
    public float LowHealthThreshold { get; set; } = 25.0f;
    public Vector4 LowHealthLineColor { get; set; } = new Vector4(1.0f, 0.8f, 0.0f, 1.0f);
    public float LowHealthLineThickness { get; set; } = 5.0f;
    public bool ShowHealthPercentage { get; set; } = true;
    public bool PulseKillableTargets { get; set; } = true;
    #endregion

    #region Enemy/Ally Detection
    public bool ShowAllies { get; set; } = false;
    public bool ShowEnemies { get; set; } = true;
    public Vector4 AllyLineColor { get; set; } = new Vector4(0.0f, 1.0f, 0.0f, 0.8f);
    public float AllyLineThickness { get; set; } = 2.0f;
    public bool ShowAllyEnemyIndicator { get; set; } = true;
    public bool DifferentColorsForAllies { get; set; } = true;
    #endregion



    #region Player List Settings
    public bool ShowPlayerList { get; set; } = false;
    public bool PlayerListShowAllies { get; set; } = true;
    public bool PlayerListShowEnemies { get; set; } = true;
    public bool PlayerListShowDistance { get; set; } = true;
    public bool PlayerListShowHealth { get; set; } = true;
    public bool PlayerListShowJob { get; set; } = true;
    public bool PlayerListShowStatus { get; set; } = true;
    public bool PlayerListAutoHide { get; set; } = false; // Hide when not in PvP
    public float PlayerListMaxDistance
    {
        get => _playerListMaxDistance;
        set => _playerListMaxDistance = Math.Clamp(value, 10.0f, 300.0f);
    }

    #endregion





    #region Color Theme System
    public Dictionary<string, Vector4> CustomColors { get; set; } = new Dictionary<string, Vector4>();

    public Vector4 GetThemedColor(Vector4 baseColor)
    {
        return ColorTheme switch
        {
            ColorTheme.Neon => ApplyNeonTheme(baseColor),
            ColorTheme.Pastel => ApplyPastelTheme(baseColor),
            ColorTheme.HighContrast => ApplyHighContrastTheme(baseColor),
            ColorTheme.ColorblindFriendly => ApplyColorblindFriendlyTheme(baseColor),
            ColorTheme.Cyberpunk => ApplyCyberpunkTheme(baseColor),
            ColorTheme.Nature => ApplyNatureTheme(baseColor),
            ColorTheme.Custom => GetCustomColor(baseColor),
            _ => baseColor
        };
    }

    private Vector4 ApplyNeonTheme(Vector4 color)
    {
        var hsv = RgbToHsv(color);
        hsv.Y = Math.Min(1.0f, hsv.Y * 1.5f);
        hsv.Z = Math.Min(1.0f, hsv.Z * 1.2f);
        return HsvToRgb(hsv);
    }

    private Vector4 ApplyPastelTheme(Vector4 color)
    {
        var hsv = RgbToHsv(color);
        hsv.Y *= 0.4f;
        hsv.Z = Math.Min(1.0f, hsv.Z * 1.1f);
        return HsvToRgb(hsv);
    }

    private Vector4 ApplyHighContrastTheme(Vector4 color)
    {
        return new Vector4(
            color.X > 0.5f ? 1.0f : 0.0f,
            color.Y > 0.5f ? 1.0f : 0.0f,
            color.Z > 0.5f ? 1.0f : 0.0f,
            color.W
        );
    }

    private Vector4 ApplyColorblindFriendlyTheme(Vector4 color)
    {
        if (color.X > color.Y && color.X > color.Z)
            return new Vector4(1.0f, 1.0f, 0.0f, color.W);
        if (color.Y > color.X && color.Y > color.Z)
            return new Vector4(0.0f, 0.5f, 1.0f, color.W);
        return color;
    }

    private Vector4 ApplyCyberpunkTheme(Vector4 color)
    {
        var hsv = RgbToHsv(color);
        hsv.X = hsv.X switch
        {
            < 0.17f => 0.5f,
            < 0.33f => 0.83f,
            < 0.67f => 0.17f,
            _ => hsv.X
        };
        hsv.Y = Math.Min(1.0f, hsv.Y * 1.3f);
        return HsvToRgb(hsv);
    }

    private Vector4 ApplyNatureTheme(Vector4 color)
    {
        var hsv = RgbToHsv(color);
        hsv.X = hsv.X switch
        {
            < 0.33f => 0.25f,
            < 0.67f => 0.58f,
            _ => 0.08f
        };
        hsv.Y *= 0.8f;
        return HsvToRgb(hsv);
    }

    private Vector4 GetCustomColor(Vector4 baseColor)
    {
        var key = $"{baseColor.X:F2}_{baseColor.Y:F2}_{baseColor.Z:F2}";
        return CustomColors.TryGetValue(key, out var customColor) ? customColor : baseColor;
    }

    private Vector3 RgbToHsv(Vector4 rgb)
    {
        float max = Math.Max(rgb.X, Math.Max(rgb.Y, rgb.Z));
        float min = Math.Min(rgb.X, Math.Min(rgb.Y, rgb.Z));
        float delta = max - min;

        float h = 0;
        if (delta != 0)
        {
            if (max == rgb.X) h = ((rgb.Y - rgb.Z) / delta) % 6;
            else if (max == rgb.Y) h = (rgb.Z - rgb.X) / delta + 2;
            else h = (rgb.X - rgb.Y) / delta + 4;
            h /= 6;
        }

        float s = max == 0 ? 0 : delta / max;
        float v = max;

        return new Vector3(h, s, v);
    }

    private Vector4 HsvToRgb(Vector3 hsv)
    {
        float c = hsv.Z * hsv.Y;
        float x = c * (1 - Math.Abs((hsv.X * 6) % 2 - 1));
        float m = hsv.Z - c;

        Vector3 rgb = hsv.X switch
        {
            < 1f/6f => new Vector3(c, x, 0),
            < 2f/6f => new Vector3(x, c, 0),
            < 3f/6f => new Vector3(0, c, x),
            < 4f/6f => new Vector3(0, x, c),
            < 5f/6f => new Vector3(x, 0, c),
            _ => new Vector3(c, 0, x)
        };

        return new Vector4(rgb.X + m, rgb.Y + m, rgb.Z + m, 1.0f);
    }

    // Dynamic Color Calculation Methods
    public Vector4 GetThreatLevelColor(float threatLevel)
    {
        if (!UseThreatLevelColors) return LineColor;

        return threatLevel switch
        {
            < 0.25f => LowThreatColor,
            < 0.5f => Vector4.Lerp(LowThreatColor, MediumThreatColor, (threatLevel - 0.25f) * 4),
            < 0.75f => Vector4.Lerp(MediumThreatColor, HighThreatColor, (threatLevel - 0.5f) * 4),
            _ => Vector4.Lerp(HighThreatColor, CriticalThreatColor, (threatLevel - 0.75f) * 4)
        };
    }

    public Vector4 GetHealthBasedColor(float healthPercentage)
    {
        if (!UseHealthBasedColors) return LineColor;

        return healthPercentage switch
        {
            > 0.66f => Vector4.Lerp(MidHealthColor, FullHealthColor, (healthPercentage - 0.66f) * 3),
            > 0.33f => Vector4.Lerp(LowHealthColor, MidHealthColor, (healthPercentage - 0.33f) * 3),
            _ => LowHealthColor
        };
    }

    public Vector4 GetTeamColor(uint grandCompanyId)
    {
        if (!UseTeamColors) return LineColor;

        return grandCompanyId switch
        {
            1 => MaelstromColor,  // Maelstrom
            2 => AdderColor,      // Order of the Twin Adder
            3 => FlamesColor,     // Immortal Flames
            _ => LineColor
        };
    }

    public float CalculateThreatLevel(PlayerDisplayData data)
    {
        float threat = 0.0f;

        // Role-based threat
        threat += data.Role switch
        {
            PlayerRole.Healer => 0.8f,      // High priority
            PlayerRole.Tank => 0.3f,        // Lower priority but tanky
            PlayerRole.MeleeDPS => 0.6f,    // Medium-high priority
            PlayerRole.RangedDPS => 0.7f,   // High priority
            PlayerRole.MagicalDPS => 0.7f,  // High priority
            _ => 0.5f
        };

        // Health-based threat modification
        if (data.IsLowHealth) threat += 0.2f; // Easier to kill
        if (data.HasDefensiveBuff) threat -= 0.3f; // Harder to kill
        if (data.IsGuarding) threat -= 0.4f; // Much harder to kill
        if (data.IsCrowdControlled) threat += 0.3f; // Vulnerable

        return Math.Clamp(threat, 0.0f, 1.0f);
    }
    #endregion
    public void Save() => Plugin.PluginInterface.SavePluginConfig(this);

    // Validation methods
    private static Vector4 ValidateColor(Vector4 color)
    {
        return new Vector4(
            Math.Clamp(color.X, 0.0f, 1.0f),
            Math.Clamp(color.Y, 0.0f, 1.0f),
            Math.Clamp(color.Z, 0.0f, 1.0f),
            Math.Clamp(color.W, 0.0f, 1.0f)
        );
    }
}
