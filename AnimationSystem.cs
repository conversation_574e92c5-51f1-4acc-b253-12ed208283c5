using System;
using System.Collections.Generic;
using System.Numerics;
using ImGuiNET;

namespace PvPLinePlugin;

public class Particle
{
    public Vector2 Position;
    public Vector2 Velocity;
    public Vector4 Color;
    public float Life;
    public float MaxLife;
    public float Size;
    public float Rotation;
    public float RotationSpeed;
    
    public bool IsAlive => Life > 0;
    
    public void Update(float deltaTime)
    {
        Position += Velocity * deltaTime;
        Life -= deltaTime;
        Rotation += RotationSpeed * deltaTime;
        
        // Fade out over time
        var alpha = Life / MaxLife;
        Color = new Vector4(Color.X, Color.Y, Color.Z, Color.W * alpha);
    }
}

public class AnimationSystem
{
    private readonly List<Particle> _particles = new();
    private readonly Random _random = new();
    private float _time = 0f;
    
    public void Update(float deltaTime)
    {
        _time += deltaTime;
        
        // Update existing particles
        for (int i = _particles.Count - 1; i >= 0; i--)
        {
            _particles[i].Update(deltaTime);
            if (!_particles[i].IsAlive)
            {
                _particles.RemoveAt(i);
            }
        }
    }
    
    public void DrawBreathingEffect(ImDrawListPtr drawList, Vector2 center, float radius, Vector4 color, float intensity)
    {
        var breatheScale = 1.0f + (float)Math.Sin(_time * 2.0f) * 0.1f * intensity;
        var breatheColor = new Vector4(color.X, color.Y, color.Z, color.W * (0.7f + 0.3f * breatheScale));
        
        drawList.AddCircle(center, radius * breatheScale, ImGui.ColorConvertFloat4ToU32(breatheColor), 32, 2.0f);
    }
    
    public void DrawPulseEffect(ImDrawListPtr drawList, Vector2 center, float radius, Vector4 color, float speed)
    {
        var pulsePhase = (_time * speed) % (2 * Math.PI);
        var pulseIntensity = (float)(0.5f + 0.5f * Math.Sin(pulsePhase));
        
        var pulseRadius = radius * (1.0f + pulseIntensity * 0.5f);
        var pulseColor = new Vector4(color.X, color.Y, color.Z, color.W * pulseIntensity);
        
        drawList.AddCircle(center, pulseRadius, ImGui.ColorConvertFloat4ToU32(pulseColor), 32, 3.0f);
    }
    
    public void DrawGlowEffect(ImDrawListPtr drawList, Vector2 start, Vector2 end, Vector4 color, float thickness, float intensity)
    {
        // Multiple layers for glow effect
        for (int i = 0; i < 5; i++)
        {
            var glowThickness = thickness + (i * 2);
            var glowAlpha = color.W * intensity * (1.0f - (i * 0.2f));
            var glowColor = new Vector4(color.X, color.Y, color.Z, glowAlpha);
            
            drawList.AddLine(start, end, ImGui.ColorConvertFloat4ToU32(glowColor), glowThickness);
        }
    }
    
    public void DrawRippleEffect(ImDrawListPtr drawList, Vector2 center, Vector4 color, float maxRadius, float speed)
    {
        var rippleTime = (_time * speed) % 2.0f;
        var currentRadius = rippleTime * maxRadius;
        var alpha = 1.0f - rippleTime;
        
        if (alpha > 0)
        {
            var rippleColor = new Vector4(color.X, color.Y, color.Z, color.W * alpha);
            drawList.AddCircle(center, currentRadius, ImGui.ColorConvertFloat4ToU32(rippleColor), 32, 2.0f);
        }
    }
    
    public void CreateSparkleEffect(Vector2 position, Vector4 color, int count = 5)
    {
        for (int i = 0; i < count; i++)
        {
            var angle = _random.NextSingle() * 2 * Math.PI;
            var speed = 50f + _random.NextSingle() * 100f;
            var velocity = new Vector2((float)Math.Cos(angle), (float)Math.Sin(angle)) * speed;
            
            var particle = new Particle
            {
                Position = position + new Vector2(
                    (_random.NextSingle() - 0.5f) * 10f,
                    (_random.NextSingle() - 0.5f) * 10f
                ),
                Velocity = velocity,
                Color = color,
                Life = 0.5f + _random.NextSingle() * 0.5f,
                MaxLife = 1.0f,
                Size = 2f + _random.NextSingle() * 3f,
                Rotation = 0f,
                RotationSpeed = (_random.NextSingle() - 0.5f) * 10f
            };
            particle.MaxLife = particle.Life;
            
            _particles.Add(particle);
        }
    }
    
    public void CreateImpactEffect(Vector2 position, Vector4 color, float intensity = 1.0f)
    {
        var particleCount = (int)(10 * intensity);
        
        for (int i = 0; i < particleCount; i++)
        {
            var angle = (float)(i * 2 * Math.PI / particleCount) + _random.NextSingle() * 0.5f;
            var speed = 100f + _random.NextSingle() * 200f * intensity;
            var velocity = new Vector2((float)Math.Cos(angle), (float)Math.Sin(angle)) * speed;
            
            var particle = new Particle
            {
                Position = position,
                Velocity = velocity,
                Color = new Vector4(color.X, color.Y, color.Z, color.W * intensity),
                Life = 0.3f + _random.NextSingle() * 0.4f,
                MaxLife = 0.7f,
                Size = 3f + _random.NextSingle() * 4f * intensity,
                Rotation = 0f,
                RotationSpeed = (_random.NextSingle() - 0.5f) * 15f
            };
            particle.MaxLife = particle.Life;
            
            _particles.Add(particle);
        }
    }
    
    public void CreateTrailEffect(Vector2 start, Vector2 end, Vector4 color, int particleCount = 8)
    {
        for (int i = 0; i < particleCount; i++)
        {
            var t = (float)i / (particleCount - 1);
            var position = Vector2.Lerp(start, end, t);
            
            var particle = new Particle
            {
                Position = position + new Vector2(
                    (_random.NextSingle() - 0.5f) * 5f,
                    (_random.NextSingle() - 0.5f) * 5f
                ),
                Velocity = Vector2.Zero,
                Color = color,
                Life = 0.2f + _random.NextSingle() * 0.3f,
                MaxLife = 0.5f,
                Size = 1f + _random.NextSingle() * 2f,
                Rotation = 0f,
                RotationSpeed = 0f
            };
            particle.MaxLife = particle.Life;
            
            _particles.Add(particle);
        }
    }
    
    public void DrawAllParticles(ImDrawListPtr drawList)
    {
        foreach (var particle in _particles)
        {
            if (particle.IsAlive)
            {
                // Draw particle as a small circle or star
                if (particle.Size > 2f)
                {
                    // Draw as star for larger particles
                    DrawStar(drawList, particle.Position, particle.Size, particle.Color, particle.Rotation);
                }
                else
                {
                    // Draw as circle for smaller particles
                    drawList.AddCircleFilled(particle.Position, particle.Size, 
                        ImGui.ColorConvertFloat4ToU32(particle.Color));
                }
            }
        }
    }
    
    private void DrawStar(ImDrawListPtr drawList, Vector2 center, float size, Vector4 color, float rotation)
    {
        var points = new Vector2[10]; // 5-pointed star
        var outerRadius = size;
        var innerRadius = size * 0.4f;
        
        for (int i = 0; i < 10; i++)
        {
            var angle = (i * Math.PI / 5) + rotation;
            var radius = (i % 2 == 0) ? outerRadius : innerRadius;
            
            points[i] = center + new Vector2(
                (float)(Math.Cos(angle) * radius),
                (float)(Math.Sin(angle) * radius)
            );
        }
        
        // Draw the star
        for (int i = 0; i < 10; i++)
        {
            var next = (i + 1) % 10;
            drawList.AddLine(points[i], points[next], ImGui.ColorConvertFloat4ToU32(color), 1.5f);
        }
    }
    
    public void DrawFlowEffect(ImDrawListPtr drawList, Vector2 start, Vector2 end, Vector4 color, float speed, int segments = 10)
    {
        var direction = Vector2.Normalize(end - start);
        var length = Vector2.Distance(start, end);
        var segmentLength = length / segments;
        
        for (int i = 0; i < segments; i++)
        {
            var t = (float)i / segments;
            var flowOffset = (_time * speed + t * 2) % 1.0f;
            var intensity = (float)(0.3f + 0.7f * Math.Sin(flowOffset * Math.PI * 2));
            
            var segmentStart = start + direction * (i * segmentLength);
            var segmentEnd = start + direction * ((i + 1) * segmentLength);
            
            var flowColor = new Vector4(color.X, color.Y, color.Z, color.W * intensity);
            drawList.AddLine(segmentStart, segmentEnd, ImGui.ColorConvertFloat4ToU32(flowColor), 2.0f);
        }
    }
    
    public void Clear()
    {
        _particles.Clear();
    }
    
    public int ParticleCount => _particles.Count;
}
