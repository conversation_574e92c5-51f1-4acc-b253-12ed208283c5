using System;
using System.Numerics;
using ImGuiNET;

namespace PvPLinePlugin;

public static class VisualEffects
{
    private static float _time = 0f;
    
    public static void UpdateTime(float deltaTime)
    {
        _time += deltaTime;
    }
    
    public static void DrawEnhancedLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, LineStyle style, Configuration config)
    {
        var themedColor = config.GetThemedColor(color);
        
        switch (style)
        {
            case LineStyle.Solid:
                DrawSolidLine(drawList, start, end, themedColor, thickness, config);
                break;
            case LineStyle.Dashed:
                DrawDashedLine(drawList, start, end, themedColor, thickness, config);
                break;
            case LineStyle.Dotted:
                DrawDottedLine(drawList, start, end, themedColor, thickness, config);
                break;
            case LineStyle.Gradient:
                DrawGradientLine(drawList, start, end, themedColor, thickness, config);
                break;
            case LineStyle.Animated:
                DrawAnimatedLine(drawList, start, end, themedColor, thickness, config);
                break;
            case LineStyle.Lightning:
                DrawLightningLine(drawList, start, end, themedColor, thickness, config);
                break;
            case LineStyle.Wavy:
                DrawWavyLine(drawList, start, end, themedColor, thickness, config);
                break;
            case LineStyle.Particles:
                DrawParticleLine(drawList, start, end, themedColor, thickness, config);
                break;
        }
        
        // Add 3D effects if enabled
        if (config.Use3DEffects)
        {
            Add3DEffects(drawList, start, end, themedColor, thickness, config);
        }
    }
    
    private static void DrawSolidLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        var finalColor = ApplyDistanceFading(color, start, end, config);
        drawList.AddLine(start, end, ImGui.ColorConvertFloat4ToU32(finalColor), thickness);
    }
    
    private static void DrawDashedLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        var direction = Vector2.Normalize(end - start);
        var length = Vector2.Distance(start, end);
        var dashLength = config.DashLength;
        var spacing = config.DashSpacing;
        var totalDashUnit = dashLength + spacing;
        
        var currentPos = start;
        var distance = 0f;
        
        while (distance < length)
        {
            var dashEnd = Vector2.Min(currentPos + direction * dashLength, end);
            var finalColor = ApplyDistanceFading(color, currentPos, dashEnd, config);
            drawList.AddLine(currentPos, dashEnd, ImGui.ColorConvertFloat4ToU32(finalColor), thickness);
            
            distance += totalDashUnit;
            currentPos = start + direction * distance;
        }
    }
    
    private static void DrawDottedLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        var direction = Vector2.Normalize(end - start);
        var length = Vector2.Distance(start, end);
        var dotSpacing = config.DashSpacing;
        var dotCount = (int)(length / dotSpacing);
        
        for (int i = 0; i <= dotCount; i++)
        {
            var t = (float)i / dotCount;
            var pos = Vector2.Lerp(start, end, t);
            var finalColor = ApplyDistanceFading(color, pos, pos, config);
            drawList.AddCircleFilled(pos, thickness / 2, ImGui.ColorConvertFloat4ToU32(finalColor));
        }
    }
    
    private static void DrawGradientLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        var segments = 20;
        var direction = (end - start) / segments;
        
        for (int i = 0; i < segments; i++)
        {
            var t = (float)i / segments;
            var segmentStart = start + direction * i;
            var segmentEnd = start + direction * (i + 1);
            
            var gradientColor = Vector4.Lerp(color, config.GradientEndColor, t);
            var finalColor = ApplyDistanceFading(gradientColor, segmentStart, segmentEnd, config);
            
            drawList.AddLine(segmentStart, segmentEnd, ImGui.ColorConvertFloat4ToU32(finalColor), thickness);
        }
    }
    
    private static void DrawAnimatedLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        var animSpeed = config.AnimationSpeed;
        var flowOffset = (_time * animSpeed) % 1.0f;
        var segments = 15;
        
        for (int i = 0; i < segments; i++)
        {
            var t1 = (float)i / segments;
            var t2 = (float)(i + 1) / segments;
            
            var segmentStart = Vector2.Lerp(start, end, t1);
            var segmentEnd = Vector2.Lerp(start, end, t2);
            
            // Create flowing effect
            var intensity = (float)(0.5f + 0.5f * Math.Sin((t1 + flowOffset) * Math.PI * 4));
            var animatedColor = new Vector4(color.X, color.Y, color.Z, color.W * intensity);
            var finalColor = ApplyDistanceFading(animatedColor, segmentStart, segmentEnd, config);
            
            drawList.AddLine(segmentStart, segmentEnd, ImGui.ColorConvertFloat4ToU32(finalColor), thickness);
        }
    }
    
    private static void DrawLightningLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        var segments = 10;
        var direction = end - start;
        var perpendicular = new Vector2(-direction.Y, direction.X);
        perpendicular = Vector2.Normalize(perpendicular);
        
        var points = new Vector2[segments + 1];
        points[0] = start;
        points[segments] = end;
        
        var random = new Random((int)(_time * 1000) % 1000);
        
        for (int i = 1; i < segments; i++)
        {
            var t = (float)i / segments;
            var basePoint = Vector2.Lerp(start, end, t);
            var offset = perpendicular * (random.NextSingle() - 0.5f) * 20f;
            points[i] = basePoint + offset;
        }
        
        for (int i = 0; i < segments; i++)
        {
            var finalColor = ApplyDistanceFading(color, points[i], points[i + 1], config);
            drawList.AddLine(points[i], points[i + 1], ImGui.ColorConvertFloat4ToU32(finalColor), thickness);
        }
    }
    
    private static void DrawWavyLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        var segments = 30;
        var direction = end - start;
        var perpendicular = new Vector2(-direction.Y, direction.X);
        perpendicular = Vector2.Normalize(perpendicular);
        
        var amplitude = config.WaveAmplitude;
        var frequency = config.WaveFrequency;
        
        for (int i = 0; i < segments; i++)
        {
            var t1 = (float)i / segments;
            var t2 = (float)(i + 1) / segments;
            
            var basePoint1 = Vector2.Lerp(start, end, t1);
            var basePoint2 = Vector2.Lerp(start, end, t2);
            
            var wave1 = (float)Math.Sin((t1 + _time * config.AnimationSpeed) * Math.PI * 2 * frequency) * amplitude;
            var wave2 = (float)Math.Sin((t2 + _time * config.AnimationSpeed) * Math.PI * 2 * frequency) * amplitude;
            
            var point1 = basePoint1 + perpendicular * wave1;
            var point2 = basePoint2 + perpendicular * wave2;
            
            var finalColor = ApplyDistanceFading(color, point1, point2, config);
            drawList.AddLine(point1, point2, ImGui.ColorConvertFloat4ToU32(finalColor), thickness);
        }
    }
    
    private static void DrawParticleLine(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        if (!config.EnableParticleEffects) return;
        
        var particleCount = (int)config.ParticleCount;
        var direction = Vector2.Normalize(end - start);
        var length = Vector2.Distance(start, end);
        
        var random = new Random((int)(_time * 100) % 1000);
        
        for (int i = 0; i < particleCount; i++)
        {
            var t = random.NextSingle();
            var pos = Vector2.Lerp(start, end, t);
            
            // Add some random offset
            var offset = new Vector2(
                (random.NextSingle() - 0.5f) * 10f,
                (random.NextSingle() - 0.5f) * 10f
            );
            pos += offset;
            
            var particleColor = new Vector4(color.X, color.Y, color.Z, color.W * random.NextSingle());
            var finalColor = ApplyDistanceFading(particleColor, pos, pos, config);
            
            drawList.AddCircleFilled(pos, thickness / 2, ImGui.ColorConvertFloat4ToU32(finalColor));
        }
        
        // Also draw a faint base line
        var baseColor = new Vector4(color.X, color.Y, color.Z, color.W * 0.3f);
        DrawSolidLine(drawList, start, end, baseColor, thickness * 0.5f, config);
    }
    
    private static void Add3DEffects(ImDrawListPtr drawList, Vector2 start, Vector2 end, 
        Vector4 color, float thickness, Configuration config)
    {
        // Add shadow
        var shadowOffset = new Vector2(config.ShadowOffset, config.ShadowOffset);
        var shadowColor = new Vector4(0, 0, 0, color.W * 0.5f);
        drawList.AddLine(start + shadowOffset, end + shadowOffset, 
            ImGui.ColorConvertFloat4ToU32(shadowColor), thickness);
        
        // Add glow effect
        if (config.GlowIntensity > 0)
        {
            var glowColor = new Vector4(color.X, color.Y, color.Z, color.W * 0.3f * config.GlowIntensity);
            drawList.AddLine(start, end, ImGui.ColorConvertFloat4ToU32(glowColor), thickness * 2);
        }
    }
    
    private static Vector4 ApplyDistanceFading(Vector4 color, Vector2 start, Vector2 end, Configuration config)
    {
        if (!config.FadeWithDistance) return color;
        
        var distance = Vector2.Distance(start, end);
        var maxDistance = config.MaxDistance;
        var fadeRatio = Math.Clamp(1.0f - (distance / maxDistance), 0.0f, 1.0f);
        
        var alpha = MathHelper.Lerp(config.MinOpacity, config.MaxOpacity, fadeRatio);
        return new Vector4(color.X, color.Y, color.Z, color.W * alpha);
    }
}

public static class MathHelper
{
    public static float Lerp(float a, float b, float t)
    {
        return a + (b - a) * Math.Clamp(t, 0.0f, 1.0f);
    }
}
